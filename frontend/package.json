{"name": "ubaswap-frontend", "version": "1.0.0", "description": "UbaSwap DEX Frontend with Wagmi + RainbowKit", "main": "index.html", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "serve": "http-server dist -p 3000"}, "dependencies": {"@rainbow-me/rainbowkit": "^1.3.0", "@tanstack/react-query": "^4.32.6", "ethers": "^5.7.2", "react": "^18.2.0", "react-dom": "^18.2.0", "viem": "^1.19.9", "wagmi": "^1.4.12"}, "devDependencies": {"@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@vitejs/plugin-react": "^4.1.0", "http-server": "^14.1.1", "typescript": "^5.2.2", "vite": "^4.5.0"}, "keywords": ["defi", "dex", "ethereum", "wagmi", "rainbowkit", "uniswap", "sepolia"], "author": "UbaSwap Team", "license": "MIT"}