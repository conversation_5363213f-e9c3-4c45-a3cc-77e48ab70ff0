import { getDefaultWallets } from '@rainbow-me/rainbowkit';
import { configureChains, createConfig } from 'wagmi';
import { sepolia } from 'wagmi/chains';
import { publicProvider } from 'wagmi/providers/public';

// Configure chains and providers
const { chains, publicClient, webSocketPublicClient } = configureChains(
  [sepolia],
  [publicProvider()]
);

// Get default wallets
const { connectors } = getDefaultWallets({
  appName: 'UbaSwap DEX',
  projectId: 'ubaswap-dex',
  chains,
});

// Create Wagmi config
const wagmiConfig = createConfig({
  autoConnect: true,
  connectors,
  publicClient,
  webSocketPublicClient,
});

// App metadata for WalletConnect
const appInfo = {
  appName: 'UbaSwap DEX',
  projectId: 'ubaswap-dex',
  description: 'Modern decentralized exchange on Ethereum',
  url: 'https://ubaswap.io',
  icons: ['🦄'],
};

export {
  chains,
  wagmiConfig,
  appInfo,
};
