// Sepolia Testnet Token Configuration
export const SEPOLIA_TOKENS = {
  ETH: {
    address: '******************************************',
    name: 'Ethereum',
    symbol: 'ETH',
    decimals: 18,
    logoURI: 'https://tokens.1inch.io/******************************************.png',
    isNative: true,
  },
  UBA: {
    address: '******************************************',
    name: 'UBA Token',
    symbol: 'UBA',
    decimals: 18,
    logoURI: '🦄',
    isNative: false,
  },
  USDC: {
    address: '******************************************',
    name: 'USD Coin',
    symbol: 'USDC',
    decimals: 6,
    logoURI: 'https://tokens.1inch.io/******************************************.png',
    isNative: false,
  },
  USDT: {
    address: '******************************************',
    name: 'Tether USD',
    symbol: 'USDT',
    decimals: 6,
    logoURI: 'https://tokens.1inch.io/******************************************.png',
    isNative: false,
  },
  DAI: {
    address: '******************************************',
    name: 'Dai Stablecoin',
    symbol: 'DAI',
    decimals: 18,
    logoURI: 'https://tokens.1inch.io/******************************************.png',
    isNative: false,
  },
  WETH: {
    address: '******************************************',
    name: 'Wrapped Ether',
    symbol: 'WETH',
    decimals: 18,
    logoURI: 'https://tokens.1inch.io/******************************************.png',
    isNative: false,
  },
  LINK: {
    address: '******************************************',
    name: 'ChainLink Token',
    symbol: 'LINK',
    decimals: 18,
    logoURI: 'https://tokens.1inch.io/******************************************.png',
    isNative: false,
  },
  UNI: {
    address: '******************************************',
    name: 'Uniswap',
    symbol: 'UNI',
    decimals: 18,
    logoURI: 'https://tokens.1inch.io/******************************************.png',
    isNative: false,
  },
  AAVE: {
    address: '******************************************',
    name: 'Aave Token',
    symbol: 'AAVE',
    decimals: 18,
    logoURI: 'https://tokens.1inch.io/******************************************.png',
    isNative: false,
  },
  COMP: {
    address: '******************************************',
    name: 'Compound',
    symbol: 'COMP',
    decimals: 18,
    logoURI: 'https://tokens.1inch.io/0xc00e94cb662c3520282e6f5717214004a7f26888.png',
    isNative: false,
  },
  MATIC: {
    address: '0x499d11E0b6eAC7c0593d8Fb292DCBbF815Fb29Ae',
    name: 'Polygon',
    symbol: 'MATIC',
    decimals: 18,
    logoURI: 'https://tokens.1inch.io/******************************************.png',
    isNative: false,
  },
  SHIB: {
    address: '******************************************',
    name: 'SHIBA INU',
    symbol: 'SHIB',
    decimals: 18,
    logoURI: 'https://tokens.1inch.io/******************************************.png',
    isNative: false,
  },
};

// Token list array for easy iteration
export const TOKEN_LIST = Object.values(SEPOLIA_TOKENS);

// Default token pairs for trading
export const DEFAULT_PAIRS = [
  ['ETH', 'UBA'],
  ['ETH', 'USDC'],
  ['ETH', 'USDT'],
  ['ETH', 'DAI'],
  ['ETH', 'WETH'],
  ['UBA', 'USDC'],
  ['UBA', 'USDT'],
  ['USDC', 'USDT'],
  ['USDC', 'DAI'],
  ['ETH', 'LINK'],
  ['ETH', 'UNI'],
  ['ETH', 'AAVE'],
];

// Get token by address
export const getTokenByAddress = (address) => {
  return TOKEN_LIST.find(token => 
    token.address.toLowerCase() === address.toLowerCase()
  );
};

// Get token by symbol
export const getTokenBySymbol = (symbol) => {
  return SEPOLIA_TOKENS[symbol.toUpperCase()];
};

// Check if token is native ETH
export const isNativeToken = (token) => {
  return token.isNative || token.address === '******************************************';
};

// Format token amount with proper decimals
export const formatTokenAmount = (amount, decimals = 18) => {
  if (!amount) return '0';
  const divisor = Math.pow(10, decimals);
  return (parseFloat(amount) / divisor).toFixed(6);
};

// Parse token amount to wei
export const parseTokenAmount = (amount, decimals = 18) => {
  if (!amount) return '0';
  const multiplier = Math.pow(10, decimals);
  return Math.floor(parseFloat(amount) * multiplier).toString();
};

// Contract addresses for DEX
export const CONTRACT_ADDRESSES = {
  UBA_TOKEN: '******************************************',
  SWAP_AND_FORWARD: '******************************************',
  LIQUIDITY_MANAGER: '******************************************',
  FEE_DISTRIBUTOR: '******************************************',
};

// Complete Smart Contract ABIs
export const SWAP_AND_FORWARD_ABI = [
  'function swapETHForTokens(uint256 minAmountOut, address tokenOut, uint256 deadline) payable returns (uint256 amountOut)',
  'function swapTokensForETH(uint256 amountIn, uint256 minAmountOut, address tokenIn, uint256 deadline) returns (uint256 amountOut)',
  'function swapTokensForTokens(uint256 amountIn, uint256 minAmountOut, address tokenIn, address tokenOut, uint256 deadline) returns (uint256 amountOut)',
  'function swapAndForward(uint256 amountIn, uint256 minAmountOut, address tokenIn, address tokenOut, address recipient, uint256 deadline) payable returns (uint256 amountOut)',
  'function getAmountOut(uint256 amountIn, address tokenIn, address tokenOut) view returns (uint256 amountOut)',
  'function protocolFeeBps() view returns (uint256)',
  'event Swap(address indexed user, address indexed tokenIn, address indexed tokenOut, uint256 amountIn, uint256 amountOut)',
  'event SwapAndForward(address indexed user, address indexed tokenIn, address indexed tokenOut, address indexed recipient, uint256 amountIn, uint256 amountOut)',
];

export const LIQUIDITY_MANAGER_ABI = [
  'function addLiquidity(uint256 amountETH, uint256 amountUBA, uint256 minETH, uint256 minUBA, address to, uint256 deadline) payable returns (uint256 liquidity)',
  'function removeLiquidity(uint256 liquidity, uint256 minETH, uint256 minUBA, address to, uint256 deadline) returns (uint256 amountETH, uint256 amountUBA)',
  'function getUserLiquidity(address user) view returns (uint256)',
  'function totalLiquidity() view returns (uint256)',
  'function getReserves() view returns (uint256 reserveETH, uint256 reserveUBA)',
  'function collectFees(address user) returns (uint256 feeETH, uint256 feeUBA)',
  'function protocolFeeBps() view returns (uint256)',
  'event LiquidityAdded(address indexed user, uint256 amountETH, uint256 amountUBA, uint256 liquidity)',
  'event LiquidityRemoved(address indexed user, uint256 amountETH, uint256 amountUBA, uint256 liquidity)',
];

export const FEE_DISTRIBUTOR_ABI = [
  'function distributeFees() returns (uint256 totalFees)',
  'function claimFees(address user) returns (uint256 amount)',
  'function getUserFees(address user) view returns (uint256)',
  'function totalFeesCollected() view returns (uint256)',
  'function protocolFeeBps() view returns (uint256)',
  'function setProtocolFee(uint256 newFeeBps) external',
  'function burnUBATokens(uint256 amount) external',
  'function owner() view returns (address)',
  'event FeesDistributed(uint256 totalAmount)',
  'event FeesClaimed(address indexed user, uint256 amount)',
  'event ProtocolFeeUpdated(uint256 oldFee, uint256 newFee)',
  'event UBATokensBurned(uint256 amount)',
];

// ERC20 ABI for token interactions
export const ERC20_ABI = [
  'function name() view returns (string)',
  'function symbol() view returns (string)',
  'function decimals() view returns (uint8)',
  'function totalSupply() view returns (uint256)',
  'function balanceOf(address) view returns (uint256)',
  'function allowance(address owner, address spender) view returns (uint256)',
  'function approve(address spender, uint256 amount) returns (bool)',
  'function transfer(address to, uint256 amount) returns (bool)',
  'function transferFrom(address from, address to, uint256 amount) returns (bool)',
  'event Transfer(address indexed from, address indexed to, uint256 value)',
  'event Approval(address indexed owner, address indexed spender, uint256 value)',
];
