/* App Layout Styles */
.app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background: var(--background-light);
  position: relative;
}

/* Main Content */
.main-content {
  flex: 1;
  padding: var(--spacing-lg) var(--spacing-md);
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
  animation: fadeIn 0.5s ease-out;
}

/* Footer */
.footer {
  background: var(--background-secondary);
  border-top: 1px solid var(--border-color);
  padding: var(--spacing-lg) var(--spacing-md);
  margin-top: auto;
}

.footer-content {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-md);
  text-align: center;
}

.footer-links {
  display: flex;
  gap: var(--spacing-lg);
  flex-wrap: wrap;
  justify-content: center;
}

.footer-link {
  color: var(--text-secondary);
  text-decoration: none;
  font-size: 0.875rem;
  transition: var(--transition-normal);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
}

.footer-link:hover {
  color: var(--text-primary);
  background: var(--surface-hover);
}

.footer-warning {
  color: var(--warning-color);
  font-size: 0.875rem;
  font-weight: 500;
  padding: var(--spacing-sm) var(--spacing-md);
  background: rgba(245, 158, 11, 0.1);
  border: 1px solid rgba(245, 158, 11, 0.2);
  border-radius: var(--radius-md);
}

/* Page Transitions */
.page-enter {
  opacity: 0;
  transform: translateY(20px);
}

.page-enter-active {
  opacity: 1;
  transform: translateY(0);
  transition: opacity 0.3s ease, transform 0.3s ease;
}

.page-exit {
  opacity: 1;
  transform: translateY(0);
}

.page-exit-active {
  opacity: 0;
  transform: translateY(-20px);
  transition: opacity 0.3s ease, transform 0.3s ease;
}

/* Card Styles */
.card {
  background: var(--surface-light);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-xl);
  padding: var(--spacing-xl);
  box-shadow: var(--shadow-sm);
  transition: var(--transition-normal);
}

.card:hover {
  border-color: var(--border-hover);
  box-shadow: var(--shadow-md);
}

.card-header {
  display: flex;
  justify-content: between;
  align-items: center;
  margin-bottom: var(--spacing-lg);
}

.card-title {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0;
}

.card-subtitle {
  color: var(--text-secondary);
  font-size: 0.875rem;
  margin: 0;
}

.card-content {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

/* Input Group Styles */
.input-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.input-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xs);
}

.input-wrapper {
  position: relative;
}

.input-field {
  width: 100%;
  background: var(--surface-light);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  padding: var(--spacing-md);
  color: var(--text-primary);
  font-size: 1rem;
  transition: var(--transition-normal);
}

.input-field:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(0, 82, 255, 0.1);
  background: var(--surface-light);
}

.input-field::placeholder {
  color: var(--text-muted);
}

.input-suffix {
  position: absolute;
  right: var(--spacing-md);
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-secondary);
  font-size: 0.875rem;
  pointer-events: none;
}

/* Button Group Styles */
.button-group {
  display: flex;
  gap: var(--spacing-sm);
}

.button-group .btn {
  flex: 1;
}

/* Status Styles */
.status {
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--radius-md);
  font-size: 0.875rem;
  font-weight: 500;
  text-align: center;
  margin-top: var(--spacing-md);
}

.status.success {
  background: rgba(34, 197, 94, 0.1);
  color: var(--success-color);
  border: 1px solid rgba(34, 197, 94, 0.2);
}

.status.error {
  background: rgba(239, 68, 68, 0.1);
  color: var(--error-color);
  border: 1px solid rgba(239, 68, 68, 0.2);
}

.status.warning {
  background: rgba(245, 158, 11, 0.1);
  color: var(--warning-color);
  border: 1px solid rgba(245, 158, 11, 0.2);
}

.status.info {
  background: rgba(59, 130, 246, 0.1);
  color: var(--info-color);
  border: 1px solid rgba(59, 130, 246, 0.2);
}

/* Loading States */
.loading {
  opacity: 0.6;
  pointer-events: none;
}

.skeleton {
  background: linear-gradient(90deg, 
    rgba(255, 255, 255, 0.05) 25%, 
    rgba(255, 255, 255, 0.1) 50%, 
    rgba(255, 255, 255, 0.05) 75%
  );
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: var(--radius-md);
}

@keyframes skeleton-loading {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

/* Responsive Design */
@media (max-width: 768px) {
  .main-content {
    padding: var(--spacing-md) var(--spacing-sm);
  }
  
  .footer-content {
    gap: var(--spacing-sm);
  }
  
  .footer-links {
    gap: var(--spacing-md);
    flex-direction: column;
    align-items: center;
  }
  
  .card {
    padding: var(--spacing-lg);
  }
  
  .button-group {
    flex-direction: column;
  }
}

@media (max-width: 480px) {
  .main-content {
    padding: var(--spacing-sm);
  }
  
  .card {
    padding: var(--spacing-md);
    border-radius: var(--radius-lg);
  }
  
  .card-title {
    font-size: 1.25rem;
  }
  
  .footer-warning {
    font-size: 0.75rem;
    padding: var(--spacing-xs) var(--spacing-sm);
  }
}

/* Dark mode specific adjustments */
@media (prefers-color-scheme: dark) {
  .app {
    color-scheme: dark;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .card {
    border-width: 2px;
  }
  
  .input-field:focus {
    border-width: 2px;
  }
  
  .footer-link:hover {
    outline: 2px solid currentColor;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
  
  .skeleton {
    animation: none;
    background: rgba(255, 255, 255, 0.1);
  }
}
