/* Navbar Styles */
.navbar {
  background: rgba(13, 20, 33, 0.95);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid var(--border-color);
  position: sticky;
  top: 0;
  z-index: var(--z-sticky);
  transition: var(--transition-normal);
}

.navbar-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-md);
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 70px;
}

/* Logo */
.navbar-logo {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  cursor: pointer;
  transition: var(--transition-normal);
  padding: var(--spacing-sm);
  border-radius: var(--radius-md);
}

.navbar-logo:hover {
  background: var(--surface-dark);
}

.logo-icon {
  font-size: 2rem;
  line-height: 1;
}

.logo-text {
  font-size: 1.5rem;
  font-weight: 700;
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Desktop Navigation */
.desktop-nav {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.nav-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--radius-md);
  background: transparent;
  border: none;
  color: var(--text-secondary);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition-normal);
  white-space: nowrap;
}

.nav-item:hover {
  background: var(--surface-dark);
  color: var(--text-primary);
}

.nav-item.active {
  background: rgba(255, 0, 122, 0.1);
  color: #ff007a;
  border: 1px solid rgba(255, 0, 122, 0.2);
}

.nav-icon {
  font-size: 1rem;
  line-height: 1;
}

.nav-label {
  font-size: 0.875rem;
}

/* Navbar Actions */
.navbar-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

/* Network Indicator */
.network-indicator {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-md);
  font-size: 0.75rem;
  font-weight: 500;
  transition: var(--transition-normal);
}

.network-indicator.correct {
  background: rgba(34, 197, 94, 0.1);
  color: var(--success-color);
  border: 1px solid rgba(34, 197, 94, 0.2);
}

.network-indicator.wrong {
  background: rgba(239, 68, 68, 0.1);
  color: var(--error-color);
  border: 1px solid rgba(239, 68, 68, 0.2);
}

.network-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: currentColor;
  animation: pulse 2s infinite;
}

.network-name {
  font-size: 0.75rem;
  font-weight: 600;
}

/* Wallet Info */
.wallet-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-md);
  background: var(--surface-dark);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  font-size: 0.875rem;
}

.wallet-address {
  color: var(--text-secondary);
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.75rem;
}

/* Connect Button Wrapper */
.connect-button-wrapper {
  display: flex;
  align-items: center;
}

/* Mobile Menu Toggle */
.mobile-menu-toggle {
  display: none;
  background: transparent;
  border: none;
  cursor: pointer;
  padding: var(--spacing-sm);
  border-radius: var(--radius-sm);
  transition: var(--transition-normal);
}

.mobile-menu-toggle:hover {
  background: var(--surface-dark);
}

.hamburger {
  display: flex;
  flex-direction: column;
  width: 20px;
  height: 16px;
  position: relative;
}

.hamburger span {
  display: block;
  height: 2px;
  width: 100%;
  background: var(--text-primary);
  border-radius: 1px;
  transition: var(--transition-normal);
  transform-origin: center;
}

.hamburger span:nth-child(1) {
  position: absolute;
  top: 0;
}

.hamburger span:nth-child(2) {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
}

.hamburger span:nth-child(3) {
  position: absolute;
  bottom: 0;
}

.hamburger.open span:nth-child(1) {
  transform: rotate(45deg) translate(5px, 5px);
}

.hamburger.open span:nth-child(2) {
  opacity: 0;
}

.hamburger.open span:nth-child(3) {
  transform: rotate(-45deg) translate(7px, -6px);
}

/* Mobile Navigation */
.mobile-nav {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: var(--background-dark);
  border-bottom: 1px solid var(--border-color);
  transform: translateY(-100%);
  opacity: 0;
  visibility: hidden;
  transition: var(--transition-normal);
  z-index: var(--z-dropdown);
}

.mobile-nav.open {
  transform: translateY(0);
  opacity: 1;
  visibility: visible;
}

.mobile-nav-content {
  padding: var(--spacing-md);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.mobile-nav-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
  border-radius: var(--radius-md);
  background: transparent;
  border: none;
  color: var(--text-secondary);
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition-normal);
  text-align: left;
  width: 100%;
}

.mobile-nav-item:hover {
  background: var(--surface-dark);
  color: var(--text-primary);
}

.mobile-nav-item.active {
  background: rgba(255, 0, 122, 0.1);
  color: #ff007a;
  border: 1px solid rgba(255, 0, 122, 0.2);
}

.mobile-nav-item .nav-icon {
  font-size: 1.25rem;
}

.mobile-nav-item .nav-label {
  font-size: 1rem;
}

/* Mobile Wallet Info */
.mobile-wallet-info {
  padding: var(--spacing-md);
  background: var(--surface-dark);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  margin-top: var(--spacing-sm);
}

.mobile-wallet-info .wallet-address {
  font-size: 0.875rem;
  color: var(--text-secondary);
}

/* Mobile Menu Overlay */
.mobile-menu-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: var(--z-modal-backdrop);
  opacity: 0;
  animation: fadeIn 0.2s ease-out forwards;
}

/* Responsive Design */
@media (max-width: 768px) {
  .navbar-container {
    padding: 0 var(--spacing-sm);
    height: 60px;
  }

  .desktop-nav {
    display: none;
  }

  .mobile-menu-toggle {
    display: flex;
  }

  .desktop-only {
    display: none;
  }

  .logo-text {
    font-size: 1.25rem;
  }

  .navbar-actions {
    gap: var(--spacing-sm);
  }

  .network-indicator {
    padding: var(--spacing-xs);
  }

  .network-name {
    display: none;
  }
}

@media (max-width: 480px) {
  .navbar-container {
    height: 56px;
  }

  .logo-icon {
    font-size: 1.5rem;
  }

  .logo-text {
    font-size: 1.125rem;
  }

  .connect-button-wrapper {
    /* RainbowKit will handle mobile responsiveness */
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .navbar {
    border-bottom-width: 2px;
  }

  .nav-item.active {
    border-width: 2px;
  }

  .network-indicator {
    border-width: 2px;
  }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  .navbar,
  .nav-item,
  .mobile-nav,
  .hamburger span,
  .network-dot {
    transition: none;
    animation: none;
  }
}
