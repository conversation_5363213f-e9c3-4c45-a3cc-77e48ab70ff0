import React, { useState } from 'react';
import './Navbar.css';

const Navbar = ({ 
  currentPage, 
  onPageChange, 
  isConnected, 
  address, 
  isCorrectNetwork,
  ConnectButton 
}) => {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  const navItems = [
    { id: 'swap', label: 'Tukar', icon: '🔄' },
    { id: 'liquidity', label: 'Likuiditas', icon: '💧' },
    { id: 'swap-forward', label: 'Tukar & Teruskan', icon: '⚡' },
    { id: 'fees', label: 'Biaya', icon: '💰' },
  ];

  const handleNavClick = (pageId) => {
    onPageChange(pageId);
    setIsMobileMenuOpen(false);
  };

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  return (
    <nav className="navbar">
      <div className="navbar-container">
        {/* Logo */}
        <div className="navbar-logo" onClick={() => handleNavClick('swap')}>
          <span className="logo-icon">🦄</span>
          <span className="logo-text">UbaSwap</span>
        </div>

        {/* Desktop Navigation */}
        <div className="navbar-nav desktop-nav">
          {navItems.map((item) => (
            <button
              key={item.id}
              className={`nav-item ${currentPage === item.id ? 'active' : ''}`}
              onClick={() => handleNavClick(item.id)}
            >
              <span className="nav-icon">{item.icon}</span>
              <span className="nav-label">{item.label}</span>
            </button>
          ))}
        </div>

        {/* Network Status & Connect Button */}
        <div className="navbar-actions">
          {/* Network Indicator */}
          {isConnected && (
            <div className={`network-indicator ${isCorrectNetwork ? 'correct' : 'wrong'}`}>
              <div className="network-dot"></div>
              <span className="network-name">
                {isCorrectNetwork ? 'Sepolia' : 'Wrong Network'}
              </span>
            </div>
          )}

          {/* Wallet Info (Desktop) */}
          {isConnected && isCorrectNetwork && (
            <div className="wallet-info desktop-only">
              <div className="wallet-address">
                {address?.slice(0, 6)}...{address?.slice(-4)}
              </div>
            </div>
          )}

          {/* Connect Button */}
          <div className="connect-button-wrapper">
            <ConnectButton 
              chainStatus="icon"
              accountStatus={{
                smallScreen: 'avatar',
                largeScreen: 'full',
              }}
              showBalance={{
                smallScreen: false,
                largeScreen: true,
              }}
            />
          </div>

          {/* Mobile Menu Toggle */}
          <button 
            className="mobile-menu-toggle"
            onClick={toggleMobileMenu}
            aria-label="Toggle mobile menu"
          >
            <span className={`hamburger ${isMobileMenuOpen ? 'open' : ''}`}>
              <span></span>
              <span></span>
              <span></span>
            </span>
          </button>
        </div>
      </div>

      {/* Mobile Navigation */}
      <div className={`mobile-nav ${isMobileMenuOpen ? 'open' : ''}`}>
        <div className="mobile-nav-content">
          {navItems.map((item) => (
            <button
              key={item.id}
              className={`mobile-nav-item ${currentPage === item.id ? 'active' : ''}`}
              onClick={() => handleNavClick(item.id)}
            >
              <span className="nav-icon">{item.icon}</span>
              <span className="nav-label">{item.label}</span>
            </button>
          ))}
          
          {/* Mobile Wallet Info */}
          {isConnected && isCorrectNetwork && (
            <div className="mobile-wallet-info">
              <div className="wallet-address">
                Connected: {address?.slice(0, 6)}...{address?.slice(-4)}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Mobile Menu Overlay */}
      {isMobileMenuOpen && (
        <div 
          className="mobile-menu-overlay"
          onClick={() => setIsMobileMenuOpen(false)}
        />
      )}
    </nav>
  );
};

export default Navbar;
