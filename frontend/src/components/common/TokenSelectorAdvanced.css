.token-selector-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(4px);
}

.token-selector-modal {
  background: #ffffff;
  border-radius: 16px;
  width: 90%;
  max-width: 480px;
  max-height: 80vh;
  overflow: hidden;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  border: 1px solid #e2e8f0;
}

.token-selector-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid #e2e8f0;
}

.token-selector-header h3 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: #0f172a;
}

.close-button {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #64748b;
  padding: 0.25rem;
  border-radius: 4px;
  transition: all 0.2s;
}

.close-button:hover {
  background: #f1f5f9;
  color: #0f172a;
}

.search-container {
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #e2e8f0;
}

.search-input {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  font-size: 1rem;
  outline: none;
  transition: all 0.2s;
  background: #f8fafc;
}

.search-input:focus {
  border-color: #0052ff;
  background: #ffffff;
  box-shadow: 0 0 0 3px rgba(0, 82, 255, 0.1);
}

.popular-tokens {
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #e2e8f0;
}

.section-title {
  font-size: 0.875rem;
  font-weight: 600;
  color: #64748b;
  margin-bottom: 0.75rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.popular-tokens-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 0.5rem;
}

.popular-token-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0.75rem 0.5rem;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  background: #ffffff;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 0.875rem;
}

.popular-token-button:hover {
  border-color: #0052ff;
  background: #f8fafc;
  transform: translateY(-1px);
}

.token-logo {
  font-size: 1.5rem;
  margin-bottom: 0.25rem;
}

.token-symbol {
  font-weight: 600;
  color: #0f172a;
}

.token-list {
  padding: 1rem 1.5rem;
  max-height: 300px;
  overflow-y: auto;
}

.token-list-container {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.token-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.2s;
  background: #ffffff;
}

.token-item:hover {
  border-color: #0052ff;
  background: #f8fafc;
  transform: translateY(-1px);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.token-item.selected {
  border-color: #0052ff;
  background: #eff6ff;
}

.token-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.token-details {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.token-name-symbol {
  display: flex;
  flex-direction: column;
}

.token-symbol {
  font-weight: 600;
  color: #0f172a;
  font-size: 1rem;
}

.token-name {
  font-size: 0.875rem;
  color: #64748b;
}

.token-price {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.price {
  font-size: 0.875rem;
  color: #0f172a;
  font-weight: 500;
}

.price-change {
  font-size: 0.75rem;
  font-weight: 600;
  padding: 0.125rem 0.375rem;
  border-radius: 4px;
  background: rgba(16, 185, 129, 0.1);
}

.token-balance {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 0.25rem;
}

.balance-amount {
  font-weight: 600;
  color: #0f172a;
  font-size: 1rem;
}

.balance-usd {
  font-size: 0.875rem;
  color: #64748b;
}

.import-token-section {
  padding: 1rem 1.5rem;
  border-top: 1px solid #e2e8f0;
  background: #f8fafc;
}

.import-token-container {
  display: flex;
  gap: 0.75rem;
  align-items: center;
}

.import-input {
  flex: 1;
  padding: 0.75rem 1rem;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  font-size: 0.875rem;
  outline: none;
  transition: all 0.2s;
}

.import-input:focus {
  border-color: #0052ff;
  box-shadow: 0 0 0 3px rgba(0, 82, 255, 0.1);
}

.import-button {
  padding: 0.75rem 1.5rem;
  background: #0052ff;
  color: white;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 0.875rem;
}

.import-button:hover:not(:disabled) {
  background: #0041cc;
  transform: translateY(-1px);
}

.import-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Scrollbar Styling */
.token-list::-webkit-scrollbar {
  width: 6px;
}

.token-list::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.token-list::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.token-list::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Mobile Responsive */
@media (max-width: 640px) {
  .token-selector-modal {
    width: 95%;
    max-height: 85vh;
  }
  
  .popular-tokens-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .token-item {
    padding: 0.75rem;
  }
  
  .import-token-container {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .import-input,
  .import-button {
    width: 100%;
  }
}
