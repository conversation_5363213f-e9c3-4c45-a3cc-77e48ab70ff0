.token-selector-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(4px);
}

.token-selector-modal {
  background: linear-gradient(135deg, #1a1b23 0%, #2d2e3f 100%);
  border-radius: 20px;
  width: 90%;
  max-width: 420px;
  max-height: 80vh;
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.modal-header h3 {
  color: #ffffff;
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
}

.close-btn {
  background: none;
  border: none;
  color: #8b949e;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  color: #ffffff;
}

.search-section {
  padding: 1rem 1.5rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.search-input {
  width: 100%;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 0.75rem 1rem;
  color: #ffffff;
  font-size: 1rem;
  transition: all 0.2s;
}

.search-input:focus {
  outline: none;
  border-color: #ff007a;
  box-shadow: 0 0 0 2px rgba(255, 0, 122, 0.2);
}

.search-input::placeholder {
  color: #8b949e;
}

.custom-import-section {
  padding: 1rem 1.5rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(255, 0, 122, 0.05);
}

.import-preview {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.75rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  border: 1px solid rgba(255, 0, 122, 0.2);
}

.import-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  flex: 1;
}

.import-icon {
  font-size: 1.5rem;
}

.import-address {
  color: #ffffff;
  font-size: 0.875rem;
  font-family: monospace;
  word-break: break-all;
}

.import-note {
  color: #8b949e;
  font-size: 0.75rem;
}

.import-btn {
  background: linear-gradient(135deg, #ff007a, #ff6b9d);
  border: none;
  color: #ffffff;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 600;
  font-size: 0.875rem;
  transition: all 0.2s;
}

.import-btn:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(255, 0, 122, 0.3);
}

.import-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.token-list {
  max-height: 300px;
  overflow-y: auto;
  padding: 0.5rem;
}

.token-list::-webkit-scrollbar {
  width: 6px;
}

.token-list::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 3px;
}

.token-list::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 3px;
}

.token-list::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}

.token-item {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.2s;
  margin-bottom: 0.25rem;
}

.token-item:hover {
  background: rgba(255, 255, 255, 0.05);
}

.token-item.selected {
  background: rgba(255, 0, 122, 0.1);
  border: 1px solid rgba(255, 0, 122, 0.3);
}

.token-icon {
  font-size: 1.5rem;
  margin-right: 0.75rem;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
}

.token-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.token-symbol {
  color: #ffffff;
  font-weight: 600;
  font-size: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.custom-badge {
  background: rgba(255, 0, 122, 0.2);
  color: #ff007a;
  padding: 0.125rem 0.5rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
}

.token-name {
  color: #8b949e;
  font-size: 0.875rem;
}

.token-balance-container {
  text-align: right;
}

.token-balance {
  color: #8b949e;
  font-size: 0.875rem;
}

.no-results {
  text-align: center;
  padding: 2rem;
  color: #8b949e;
}

.no-results p {
  margin: 0.5rem 0;
}

.popular-tokens {
  padding: 1rem 1.5rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.popular-tokens h4 {
  color: #ffffff;
  margin: 0 0 1rem 0;
  font-size: 0.875rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.popular-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 0.5rem;
}

.popular-token-btn {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 0.5rem;
  color: #ffffff;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
}

.popular-token-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 0, 122, 0.3);
}

.popular-icon {
  font-size: 1rem;
}

/* Responsive */
@media (max-width: 480px) {
  .token-selector-modal {
    width: 95%;
    max-height: 85vh;
  }
  
  .modal-header {
    padding: 1rem;
  }
  
  .search-section {
    padding: 0.75rem 1rem;
  }
  
  .custom-import-section {
    padding: 0.75rem 1rem;
  }
  
  .popular-tokens {
    padding: 0.75rem 1rem;
  }
  
  .popular-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}
