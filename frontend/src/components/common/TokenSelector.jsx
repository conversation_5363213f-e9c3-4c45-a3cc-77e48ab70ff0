import React, { useState, useEffect } from 'react';
import { useBalance, useReadContract } from 'wagmi';
import { formatUnits } from 'viem';
import { CONTRACT_ADDRESSES, TOKENS } from '../../config/contracts';
import './TokenSelector.css';

const TokenSelector = ({ 
  isOpen, 
  onClose, 
  onSelect, 
  selectedToken, 
  address, 
  isConnected, 
  isCorrectNetwork,
  showToast 
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [customTokens, setCustomTokens] = useState([]);
  const [isImporting, setIsImporting] = useState(false);

  // All available tokens (default + custom)
  const allTokens = { ...TOKENS, ...customTokens.reduce((acc, token) => ({ ...acc, [token.symbol]: token }), {}) };

  // Filter tokens based on search query
  const filteredTokens = Object.entries(allTokens).filter(([symbol, token]) => {
    const query = searchQuery.toLowerCase();
    return (
      symbol.toLowerCase().includes(query) ||
      token.name.toLowerCase().includes(query) ||
      token.address.toLowerCase().includes(query)
    );
  });

  // Check if search query is a valid Ethereum address
  const isValidAddress = (address) => {
    return /^0x[a-fA-F0-9]{40}$/.test(address);
  };

  // Import custom token
  const importCustomToken = async (tokenAddress) => {
    if (!isValidAddress(tokenAddress)) {
      showToast({
        type: 'error',
        title: 'Invalid Address',
        message: 'Please enter a valid Ethereum address',
      });
      return;
    }

    setIsImporting(true);

    try {
      // Create a contract instance to get token info
      const response = await fetch(`https://api.etherscan.io/api?module=contract&action=getabi&address=${tokenAddress}&apikey=YourApiKeyToken`);
      
      // For demo purposes, we'll use a simplified approach
      // In production, you'd want to use a proper contract call
      const tokenInfo = {
        address: tokenAddress,
        symbol: 'CUSTOM',
        name: 'Custom Token',
        decimals: 18,
        icon: '🪙',
        color: '#888888',
        isNative: false,
        isCustom: true
      };

      // Add to custom tokens
      const newCustomTokens = [...customTokens, tokenInfo];
      setCustomTokens(newCustomTokens);
      
      // Store in localStorage
      localStorage.setItem('customTokens', JSON.stringify(newCustomTokens));

      showToast({
        type: 'success',
        title: 'Token Imported',
        message: `${tokenInfo.symbol} has been added to your token list`,
      });

      setSearchQuery('');
    } catch (error) {
      showToast({
        type: 'error',
        title: 'Import Failed',
        message: 'Failed to import token. Please check the address.',
      });
    } finally {
      setIsImporting(false);
    }
  };

  // Load custom tokens from localStorage on mount
  useEffect(() => {
    const stored = localStorage.getItem('customTokens');
    if (stored) {
      try {
        setCustomTokens(JSON.parse(stored));
      } catch (error) {
        console.error('Failed to load custom tokens:', error);
      }
    }
  }, []);

  // Token balance component
  const TokenBalance = ({ token }) => {
    const { data: balance } = useBalance({
      address,
      token: token.isNative ? undefined : token.address,
      enabled: isConnected && isCorrectNetwork && Boolean(address),
    });

    if (!balance) return <span className="token-balance">0.0000</span>;

    return (
      <span className="token-balance">
        {parseFloat(balance.formatted).toFixed(4)}
      </span>
    );
  };

  if (!isOpen) return null;

  return (
    <div className="token-selector-overlay" onClick={onClose}>
      <div className="token-selector-modal" onClick={(e) => e.stopPropagation()}>
        <div className="modal-header">
          <h3>Select Token</h3>
          <button className="close-btn" onClick={onClose}>×</button>
        </div>

        {/* Search Input */}
        <div className="search-section">
          <input
            type="text"
            placeholder="Search by name, symbol, or paste address..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="search-input"
          />
        </div>

        {/* Custom Token Import */}
        {isValidAddress(searchQuery) && !allTokens[searchQuery] && (
          <div className="custom-import-section">
            <div className="import-preview">
              <div className="import-info">
                <span className="import-icon">🪙</span>
                <div>
                  <div className="import-address">{searchQuery}</div>
                  <div className="import-note">Unknown token</div>
                </div>
              </div>
              <button
                className="import-btn"
                onClick={() => importCustomToken(searchQuery)}
                disabled={isImporting}
              >
                {isImporting ? 'Importing...' : 'Import'}
              </button>
            </div>
          </div>
        )}

        {/* Token List */}
        <div className="token-list">
          {filteredTokens.length === 0 ? (
            <div className="no-results">
              <p>No tokens found</p>
              {isValidAddress(searchQuery) && (
                <p>Try importing the token above</p>
              )}
            </div>
          ) : (
            filteredTokens.map(([symbol, token]) => (
              <div
                key={symbol}
                className={`token-item ${selectedToken === symbol ? 'selected' : ''}`}
                onClick={() => {
                  onSelect(symbol);
                  onClose();
                }}
              >
                <div className="token-icon" style={{ color: token.color }}>
                  {token.icon}
                </div>
                <div className="token-info">
                  <div className="token-symbol">
                    {symbol}
                    {token.isCustom && <span className="custom-badge">Custom</span>}
                  </div>
                  <div className="token-name">{token.name}</div>
                </div>
                <div className="token-balance-container">
                  <TokenBalance token={token} />
                </div>
              </div>
            ))
          )}
        </div>

        {/* Popular Tokens */}
        {!searchQuery && (
          <div className="popular-tokens">
            <h4>Popular Tokens</h4>
            <div className="popular-grid">
              {['ETH', 'UBA', 'WETH', 'USDC', 'LINK', 'UNI'].map(symbol => (
                <button
                  key={symbol}
                  className="popular-token-btn"
                  onClick={() => {
                    onSelect(symbol);
                    onClose();
                  }}
                >
                  <span className="popular-icon">{TOKENS[symbol]?.icon}</span>
                  <span>{symbol}</span>
                </button>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default TokenSelector;
