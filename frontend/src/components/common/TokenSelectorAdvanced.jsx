import React, { useState, useEffect } from 'react';
import { useAccount, useContractRead } from 'wagmi';
import { SEPOLIA_TOKENS, TOKEN_LIST, ERC20_ABI, getTokenByAddress, isNativeToken } from '../../config/tokens';
import { useTokenBalance, useTokenPrice } from '../../hooks/useTokenBalance';
import './TokenSelectorAdvanced.css';

const TokenSelectorAdvanced = ({ 
  isOpen, 
  onClose, 
  onSelect, 
  selectedToken, 
  showToast 
}) => {
  const { address, isConnected } = useAccount();
  const [searchQuery, setSearchQuery] = useState('');
  const [customTokens, setCustomTokens] = useState([]);
  const [isImporting, setIsImporting] = useState(false);
  const [importAddress, setImportAddress] = useState('');

  // All available tokens (default + custom)
  const allTokens = [...TOKEN_LIST, ...customTokens];

  // Filter tokens based on search query
  const filteredTokens = allTokens.filter(token => {
    const query = searchQuery.toLowerCase();
    return (
      token.symbol.toLowerCase().includes(query) ||
      token.name.toLowerCase().includes(query) ||
      token.address.toLowerCase().includes(query)
    );
  });

  // Check if search query is a valid Ethereum address
  const isValidAddress = (addr) => {
    return /^0x[a-fA-F0-9]{40}$/.test(addr);
  };

  // Import custom token
  const { data: tokenInfo, isLoading: isLoadingToken } = useContractRead({
    address: isValidAddress(importAddress) ? importAddress : undefined,
    abi: ERC20_ABI,
    functionName: 'symbol',
    enabled: isValidAddress(importAddress) && !getTokenByAddress(importAddress),
  });

  const { data: tokenName } = useContractRead({
    address: isValidAddress(importAddress) ? importAddress : undefined,
    abi: ERC20_ABI,
    functionName: 'name',
    enabled: isValidAddress(importAddress) && !getTokenByAddress(importAddress),
  });

  const { data: tokenDecimals } = useContractRead({
    address: isValidAddress(importAddress) ? importAddress : undefined,
    abi: ERC20_ABI,
    functionName: 'decimals',
    enabled: isValidAddress(importAddress) && !getTokenByAddress(importAddress),
  });

  const handleImportToken = async () => {
    if (!isValidAddress(importAddress)) {
      showToast({
        type: 'error',
        title: 'Invalid Address',
        message: 'Please enter a valid Ethereum address',
      });
      return;
    }

    if (getTokenByAddress(importAddress)) {
      showToast({
        type: 'warning',
        title: 'Token Already Added',
        message: 'This token is already in your list',
      });
      return;
    }

    setIsImporting(true);

    try {
      if (tokenInfo && tokenName && tokenDecimals) {
        const newToken = {
          address: importAddress,
          name: tokenName,
          symbol: tokenInfo,
          decimals: tokenDecimals,
          logoURI: '🪙',
          isNative: false,
          isCustom: true,
        };

        setCustomTokens(prev => [...prev, newToken]);
        setImportAddress('');
        
        showToast({
          type: 'success',
          title: 'Token Imported',
          message: `${tokenInfo} has been added to your token list`,
        });
      }
    } catch (error) {
      showToast({
        type: 'error',
        title: 'Import Failed',
        message: 'Failed to import token. Please check the address.',
      });
    } finally {
      setIsImporting(false);
    }
  };

  const handleTokenSelect = (token) => {
    onSelect(token);
    onClose();
    setSearchQuery('');
  };

  if (!isOpen) return null;

  return (
    <div className="token-selector-overlay" onClick={onClose}>
      <div className="token-selector-modal" onClick={e => e.stopPropagation()}>
        <div className="token-selector-header">
          <h3>Select a Token</h3>
          <button className="close-button" onClick={onClose}>×</button>
        </div>

        {/* Search Input */}
        <div className="search-container">
          <input
            type="text"
            placeholder="Search name, symbol, or paste address"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="search-input"
          />
        </div>

        {/* Popular Tokens */}
        <div className="popular-tokens">
          <div className="section-title">Popular Tokens</div>
          <div className="popular-tokens-grid">
            {[SEPOLIA_TOKENS.ETH, SEPOLIA_TOKENS.UBA, SEPOLIA_TOKENS.USDC, SEPOLIA_TOKENS.USDT].map(token => (
              <button
                key={token.symbol}
                className="popular-token-button"
                onClick={() => handleTokenSelect(token)}
              >
                <span className="token-logo">{token.logoURI}</span>
                <span className="token-symbol">{token.symbol}</span>
              </button>
            ))}
          </div>
        </div>

        {/* Token List */}
        <div className="token-list">
          <div className="section-title">All Tokens</div>
          <div className="token-list-container">
            {filteredTokens.map(token => (
              <TokenItem
                key={token.address}
                token={token}
                onSelect={handleTokenSelect}
                isSelected={selectedToken?.address === token.address}
                userAddress={address}
                isConnected={isConnected}
              />
            ))}
          </div>
        </div>

        {/* Import Token Section */}
        {isValidAddress(searchQuery) && !getTokenByAddress(searchQuery) && (
          <div className="import-token-section">
            <div className="section-title">Import Token</div>
            <div className="import-token-container">
              <input
                type="text"
                placeholder="Token contract address"
                value={importAddress}
                onChange={(e) => setImportAddress(e.target.value)}
                className="import-input"
              />
              <button
                className="import-button"
                onClick={handleImportToken}
                disabled={isImporting || isLoadingToken}
              >
                {isImporting ? 'Importing...' : 'Import'}
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

// Token Item Component
const TokenItem = ({ token, onSelect, isSelected, userAddress, isConnected }) => {
  const { balance, isLoading } = useTokenBalance(token);
  const { price, priceChange24h, priceChangeColor } = useTokenPrice(token.symbol);

  return (
    <div
      className={`token-item ${isSelected ? 'selected' : ''}`}
      onClick={() => onSelect(token)}
    >
      <div className="token-info">
        <div className="token-logo">{token.logoURI}</div>
        <div className="token-details">
          <div className="token-name-symbol">
            <span className="token-symbol">{token.symbol}</span>
            <span className="token-name">{token.name}</span>
          </div>
          <div className="token-price">
            <span className="price">${price.toFixed(price < 1 ? 6 : 2)}</span>
            <span 
              className="price-change" 
              style={{ color: priceChangeColor }}
            >
              {priceChange24h >= 0 ? '+' : ''}{priceChange24h.toFixed(2)}%
            </span>
          </div>
        </div>
      </div>
      
      {isConnected && (
        <div className="token-balance">
          <div className="balance-amount">
            {isLoading ? '...' : parseFloat(balance).toFixed(6)}
          </div>
          <div className="balance-usd">
            ${(parseFloat(balance) * price).toFixed(2)}
          </div>
        </div>
      )}
    </div>
  );
};

export default TokenSelectorAdvanced;
