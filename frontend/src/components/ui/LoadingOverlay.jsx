import React from 'react';
import './LoadingOverlay.css';

const LoadingOverlay = ({ message = 'Processing transaction...', isVisible = true }) => {
  if (!isVisible) return null;

  return (
    <div className="loading-overlay">
      <div className="loading-content">
        <div className="loading-spinner">
          <div className="spinner-ring"></div>
          <div className="spinner-ring"></div>
          <div className="spinner-ring"></div>
        </div>
        <div className="loading-text">{message}</div>
        <div className="loading-subtitle">
          Please confirm the transaction in your wallet
        </div>
      </div>
    </div>
  );
};

export default LoadingOverlay;
