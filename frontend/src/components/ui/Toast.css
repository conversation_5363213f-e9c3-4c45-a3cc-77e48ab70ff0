/* Toast Styles */
.toast {
  position: fixed;
  top: var(--spacing-lg);
  right: var(--spacing-lg);
  min-width: 320px;
  max-width: 480px;
  background: var(--background-dark);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-xl);
  backdrop-filter: blur(20px);
  z-index: var(--z-toast);
  transform: translateX(100%);
  opacity: 0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
}

.toast.visible {
  transform: translateX(0);
  opacity: 1;
}

.toast.exiting {
  transform: translateX(100%);
  opacity: 0;
}

.toast-content {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-sm);
  padding: var(--spacing-md);
}

.toast-icon {
  font-size: 1.25rem;
  line-height: 1;
  flex-shrink: 0;
  margin-top: 2px;
}

.toast-text {
  flex: 1;
  min-width: 0;
}

.toast-title {
  font-weight: 600;
  font-size: 0.875rem;
  margin-bottom: var(--spacing-xs);
  color: var(--text-primary);
}

.toast-message {
  font-size: 0.875rem;
  line-height: 1.4;
  color: var(--text-secondary);
  word-wrap: break-word;
}

.toast-close {
  background: transparent;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  padding: var(--spacing-xs);
  border-radius: var(--radius-sm);
  transition: var(--transition-normal);
  flex-shrink: 0;
  font-size: 0.875rem;
  line-height: 1;
}

.toast-close:hover {
  background: var(--surface-dark);
  color: var(--text-primary);
}

.toast-progress {
  position: absolute;
  bottom: 0;
  left: 0;
  height: 3px;
  background: currentColor;
  animation: toast-progress linear;
  transform-origin: left;
}

@keyframes toast-progress {
  from {
    transform: scaleX(1);
  }
  to {
    transform: scaleX(0);
  }
}

/* Toast Type Variants */
.toast-success {
  border-left: 4px solid var(--success-color);
}

.toast-success .toast-progress {
  background: var(--success-color);
}

.toast-error {
  border-left: 4px solid var(--error-color);
}

.toast-error .toast-progress {
  background: var(--error-color);
}

.toast-warning {
  border-left: 4px solid var(--warning-color);
}

.toast-warning .toast-progress {
  background: var(--warning-color);
}

.toast-info {
  border-left: 4px solid var(--info-color);
}

.toast-info .toast-progress {
  background: var(--info-color);
}

/* Responsive Design */
@media (max-width: 768px) {
  .toast {
    top: var(--spacing-md);
    right: var(--spacing-md);
    left: var(--spacing-md);
    min-width: auto;
    max-width: none;
  }
}

@media (max-width: 480px) {
  .toast {
    top: var(--spacing-sm);
    right: var(--spacing-sm);
    left: var(--spacing-sm);
  }
  
  .toast-content {
    padding: var(--spacing-sm);
  }
  
  .toast-title,
  .toast-message {
    font-size: 0.8rem;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .toast {
    border-width: 2px;
  }
  
  .toast-success {
    border-left-width: 6px;
  }
  
  .toast-error {
    border-left-width: 6px;
  }
  
  .toast-warning {
    border-left-width: 6px;
  }
  
  .toast-info {
    border-left-width: 6px;
  }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  .toast {
    transition: opacity 0.2s ease;
    transform: none;
  }
  
  .toast.visible {
    transform: none;
  }
  
  .toast.exiting {
    transform: none;
  }
  
  .toast-progress {
    animation: none;
    display: none;
  }
}
