/* Loading Overlay Styles */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(8px);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: var(--z-modal);
  animation: fadeIn 0.3s ease-out;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-lg);
  padding: var(--spacing-2xl);
  background: var(--background-dark);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-xl);
  max-width: 400px;
  text-align: center;
}

.loading-spinner {
  position: relative;
  width: 80px;
  height: 80px;
}

.spinner-ring {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 3px solid transparent;
  border-radius: 50%;
  animation: spin 1.5s linear infinite;
}

.spinner-ring:nth-child(1) {
  border-top-color: #ff007a;
  animation-delay: 0s;
}

.spinner-ring:nth-child(2) {
  border-right-color: #ff6b9d;
  animation-delay: -0.5s;
  width: 90%;
  height: 90%;
  top: 5%;
  left: 5%;
}

.spinner-ring:nth-child(3) {
  border-bottom-color: #ff007a;
  animation-delay: -1s;
  width: 80%;
  height: 80%;
  top: 10%;
  left: 10%;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.loading-text {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.loading-subtitle {
  font-size: 0.875rem;
  color: var(--text-secondary);
  margin: 0;
  line-height: 1.4;
}

/* Alternative simple spinner */
.loading-spinner.simple {
  width: 50px;
  height: 50px;
  border: 3px solid rgba(255, 0, 122, 0.3);
  border-top: 3px solid #ff007a;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-spinner.simple .spinner-ring {
  display: none;
}

/* Pulsing dots animation */
.loading-dots {
  display: flex;
  gap: var(--spacing-xs);
  justify-content: center;
  margin-top: var(--spacing-md);
}

.loading-dot {
  width: 8px;
  height: 8px;
  background: #ff007a;
  border-radius: 50%;
  animation: pulse-dot 1.4s ease-in-out infinite both;
}

.loading-dot:nth-child(1) {
  animation-delay: -0.32s;
}

.loading-dot:nth-child(2) {
  animation-delay: -0.16s;
}

.loading-dot:nth-child(3) {
  animation-delay: 0s;
}

@keyframes pulse-dot {
  0%, 80%, 100% {
    transform: scale(0);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .loading-content {
    padding: var(--spacing-xl);
    margin: var(--spacing-md);
    max-width: none;
  }
  
  .loading-spinner {
    width: 60px;
    height: 60px;
  }
  
  .loading-text {
    font-size: 1.125rem;
  }
  
  .loading-subtitle {
    font-size: 0.8rem;
  }
}

@media (max-width: 480px) {
  .loading-content {
    padding: var(--spacing-lg);
    margin: var(--spacing-sm);
  }
  
  .loading-spinner {
    width: 50px;
    height: 50px;
  }
  
  .loading-text {
    font-size: 1rem;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .loading-overlay {
    background: rgba(0, 0, 0, 0.9);
  }
  
  .loading-content {
    border-width: 2px;
  }
  
  .spinner-ring {
    border-width: 4px;
  }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  .loading-overlay {
    animation: none;
  }
  
  .spinner-ring {
    animation: none;
    border-top-color: #ff007a;
    border-right-color: transparent;
    border-bottom-color: transparent;
    border-left-color: transparent;
  }
  
  .loading-dot {
    animation: none;
    opacity: 1;
    transform: scale(1);
  }
  
  .loading-spinner.simple {
    animation: none;
    border-top-color: #ff007a;
  }
}
