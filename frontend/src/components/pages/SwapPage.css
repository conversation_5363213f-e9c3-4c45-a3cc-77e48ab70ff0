/* Swap Page Styles */
.swap-page {
  max-width: 600px;
  margin: 0 auto;
  padding: var(--spacing-lg) 0;
}

.page-header {
  text-align: center;
  margin-bottom: var(--spacing-2xl);
}

.page-header h1 {
  font-size: 2.5rem;
  font-weight: 700;
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: var(--spacing-sm);
}

.page-header p {
  color: var(--text-secondary);
  font-size: 1.125rem;
  margin: 0;
}

.swap-container {
  display: flex;
  justify-content: center;
}

.swap-card {
  background: var(--surface-light);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-xl);
  padding: var(--spacing-xl);
  width: 100%;
  max-width: 480px;
  box-shadow: var(--shadow-sm);
  transition: var(--transition-normal);
}

.swap-card:hover {
  border-color: var(--border-hover);
  box-shadow: var(--shadow-md);
}

.swap-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-xl);
}

.swap-header h2 {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0;
}

.settings-btn {
  background: var(--surface-light);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  padding: var(--spacing-sm);
  color: var(--text-secondary);
  cursor: pointer;
  transition: var(--transition-normal);
  font-size: 1.125rem;
}

.settings-btn:hover {
  background: var(--surface-hover);
  color: var(--text-primary);
}

/* Token Input Groups */
.token-input-group {
  margin-bottom: var(--spacing-lg);
}

.token-input-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-sm);
}

.token-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-secondary);
}

.token-balance {
  font-size: 0.875rem;
  color: var(--text-secondary);
  cursor: pointer;
  transition: var(--transition-normal);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
}

.token-balance:hover {
  background: var(--surface-hover);
  color: var(--text-primary);
}

.token-input {
  display: flex;
  align-items: center;
  background: var(--surface-light);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  padding: var(--spacing-md);
  transition: var(--transition-normal);
}

.token-input:focus-within {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(0, 82, 255, 0.1);
}

.amount-input {
  flex: 1;
  background: transparent;
  border: none;
  color: var(--text-primary);
  font-size: 1.5rem;
  font-weight: 500;
  outline: none;
  margin-right: var(--spacing-md);
}

.amount-input::placeholder {
  color: var(--text-muted);
}

.token-selector {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  background: var(--surface-light);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  padding: var(--spacing-sm) var(--spacing-md);
  color: var(--text-primary);
  cursor: pointer;
  transition: var(--transition-normal);
  font-weight: 500;
}

.token-selector:hover {
  background: var(--surface-hover);
  border-color: var(--border-hover);
}

.token-icon {
  font-size: 1.25rem;
  line-height: 1;
}

.token-symbol {
  font-size: 1rem;
  font-weight: 600;
}

.dropdown-arrow {
  font-size: 0.75rem;
  color: var(--text-secondary);
  transition: var(--transition-normal);
}

.token-selector:hover .dropdown-arrow {
  color: var(--text-primary);
}

/* Swap Arrow */
.swap-arrow-container {
  display: flex;
  justify-content: center;
  margin: var(--spacing-md) 0;
  position: relative;
}

.swap-arrow {
  background: var(--surface-light);
  border: 2px solid var(--border-color);
  border-radius: 50%;
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
  color: var(--text-secondary);
  cursor: pointer;
  transition: var(--transition-normal);
  position: relative;
  z-index: 1;
}

.swap-arrow:hover {
  background: var(--surface-hover);
  border-color: #ff007a;
  color: #ff007a;
  transform: rotate(180deg);
}

/* Price Info */
.price-info {
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  padding: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
}

.price-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-sm);
  font-size: 0.875rem;
}

.price-row:last-child {
  margin-bottom: 0;
}

.price-row span:first-child {
  color: var(--text-secondary);
}

.price-row span:last-child {
  color: var(--text-primary);
  font-weight: 500;
}

/* Swap Button */
.swap-btn {
  width: 100%;
  padding: var(--spacing-lg);
  border-radius: var(--radius-lg);
  font-size: 1.125rem;
  font-weight: 600;
  border: none;
  cursor: pointer;
  transition: var(--transition-normal);
  margin-top: var(--spacing-lg);
}

.swap-btn.enabled {
  background: var(--primary-gradient);
  color: var(--text-primary);
}

.swap-btn.enabled:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(255, 0, 122, 0.3);
}

.swap-btn.disabled {
  background: var(--surface-dark);
  color: var(--text-muted);
  cursor: not-allowed;
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: var(--z-modal);
  animation: fadeIn 0.2s ease-out;
}

.modal-content {
  background: var(--background-dark);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-xl);
  padding: var(--spacing-xl);
  max-width: 400px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-lg);
}

.modal-header h3 {
  margin: 0;
  font-size: 1.25rem;
}

.modal-header button {
  background: transparent;
  border: none;
  color: var(--text-secondary);
  font-size: 1.25rem;
  cursor: pointer;
  padding: var(--spacing-xs);
  border-radius: var(--radius-sm);
  transition: var(--transition-normal);
}

.modal-header button:hover {
  background: var(--surface-dark);
  color: var(--text-primary);
}

/* Settings Content */
.settings-content {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.setting-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.setting-group label {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-secondary);
}

.slippage-options {
  display: flex;
  gap: var(--spacing-sm);
}

.slippage-btn {
  flex: 1;
  padding: var(--spacing-sm);
  background: var(--surface-dark);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  color: var(--text-secondary);
  cursor: pointer;
  transition: var(--transition-normal);
  font-size: 0.875rem;
}

.slippage-btn:hover {
  background: var(--surface-hover);
  color: var(--text-primary);
}

.slippage-btn.active {
  background: rgba(255, 0, 122, 0.2);
  border-color: #ff007a;
  color: #ff007a;
}

.deadline-input {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.deadline-input input {
  flex: 1;
  background: var(--surface-dark);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  padding: var(--spacing-sm);
  color: var(--text-primary);
}

.deadline-input span {
  color: var(--text-secondary);
  font-size: 0.875rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .swap-page {
    padding: var(--spacing-md) 0;
  }
  
  .page-header h1 {
    font-size: 2rem;
  }
  
  .swap-card {
    padding: var(--spacing-lg);
    margin: 0 var(--spacing-sm);
  }
  
  .amount-input {
    font-size: 1.25rem;
  }
  
  .modal-content {
    padding: var(--spacing-lg);
    margin: var(--spacing-sm);
  }
}

@media (max-width: 480px) {
  .page-header h1 {
    font-size: 1.75rem;
  }
  
  .swap-card {
    padding: var(--spacing-md);
  }
  
  .token-input {
    padding: var(--spacing-sm);
  }
  
  .amount-input {
    font-size: 1.125rem;
  }
  
  .slippage-options {
    flex-direction: column;
  }
}

/* Real-time Data Features */
.real-time-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: #10b981;
  font-weight: 500;
}

.indicator-dot {
  width: 8px;
  height: 8px;
  background: #10b981;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}

.slippage-container {
  margin: 1rem 0;
  padding: 1rem;
  background: #f8fafc;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
}

.slippage-container label {
  display: block;
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
  font-weight: 600;
  color: #0f172a;
}

.slippage-container input[type="range"] {
  width: 100%;
  height: 6px;
  background: #e2e8f0;
  border-radius: 3px;
  outline: none;
  -webkit-appearance: none;
}

.slippage-container input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  width: 18px;
  height: 18px;
  background: #0052ff;
  border-radius: 50%;
  cursor: pointer;
}

.slippage-container input[type="range"]::-moz-range-thumb {
  width: 18px;
  height: 18px;
  background: #0052ff;
  border-radius: 50%;
  cursor: pointer;
  border: none;
}

.real-time-status {
  margin-top: 1rem;
  padding: 1rem;
  background: #f0f9ff;
  border: 1px solid #bae6fd;
  border-radius: 12px;
  font-size: 0.875rem;
  color: #0369a1;
}

.real-time-status div {
  margin-bottom: 0.25rem;
}

.real-time-status div:last-child {
  margin-bottom: 0;
}

.token-value {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 0.875rem;
  color: #64748b;
  margin-top: 0.5rem;
}
