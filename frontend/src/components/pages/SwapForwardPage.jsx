import React, { useState, useEffect } from 'react';
import {
  useBalance,
  useReadContract,
  useWriteContract,
  useWaitForTransactionReceipt
} from 'wagmi';
import { parseEther, formatEther, isAddress } from 'viem';
import { CONTRACT_ADDRESSES, CONTRACT_ABIS, TOKENS, MESSAGES, FEE_TIERS } from '../../config/contracts';
import TokenSelector from '../common/TokenSelector';
import './SwapForwardPage.css';

const SwapForwardPage = ({ isConnected, address, isCorrectNetwork, settings, updateSettings, showToast, setIsLoading }) => {
  // State
  const [tokenIn, setTokenIn] = useState('ETH');
  const [tokenOut, setTokenOut] = useState('UBA');
  const [amountIn, setAmountIn] = useState('');
  const [amountOut, setAmountOut] = useState('');
  const [useCustomRecipient, setUseCustomRecipient] = useState(false);
  const [recipientAddress, setRecipientAddress] = useState('');
  const [showSettings, setShowSettings] = useState(false);
  const [needsApproval, setNeedsApproval] = useState(false);
  const [showTokenSelector, setShowTokenSelector] = useState(false);
  const [tokenSelectorType, setTokenSelectorType] = useState('in'); // 'in' or 'out'

  // Balance hooks
  const { data: ethBalance, refetch: refetchEthBalance } = useBalance({
    address,
    query: {
      enabled: isConnected && isCorrectNetwork,
    }
  });

  const { data: ubaBalance, refetch: refetchUbaBalance } = useBalance({
    address,
    token: CONTRACT_ADDRESSES.UBA_TOKEN,
    query: {
      enabled: isConnected && isCorrectNetwork,
    }
  });

  // UBA token allowance check
  const { data: allowance, refetch: refetchAllowance } = useReadContract({
    address: CONTRACT_ADDRESSES.UBA_TOKEN,
    abi: CONTRACT_ABIS.UBA_TOKEN,
    functionName: 'allowance',
    args: [address, CONTRACT_ADDRESSES.SWAP_AND_FORWARD],
    query: {
      enabled: isConnected && isCorrectNetwork && tokenIn === 'UBA',
    }
  });

  // Contract writes
  const {
    data: approveHash,
    writeContract: approveWrite,
    isPending: isApproveLoading
  } = useWriteContract();

  const {
    data: swapHash,
    writeContract: swapWrite,
    isPending: isSwapLoading
  } = useWriteContract();

  // Wait for transactions
  const { isLoading: isApproveWaiting } = useWaitForTransactionReceipt({
    hash: approveHash,
  });

  const { isLoading: isSwapWaiting } = useWaitForTransactionReceipt({
    hash: swapHash,
  });

  // Handle transaction success
  useEffect(() => {
    if (approveHash && !isApproveWaiting) {
      showToast({
        type: 'success',
        title: 'Approval Successful!',
        message: 'Token approval completed. You can now swap & forward.',
      });
      refetchAllowance();
    }
  }, [approveHash, isApproveWaiting]);

  useEffect(() => {
    if (swapHash && !isSwapWaiting) {
      showToast({
        type: 'success',
        title: 'Swap & Forward Successful!',
        message: `Swapped ${amountIn} ${tokenIn} and sent ${amountOut} ${tokenOut} to recipient`,
      });
      // Clear form and refresh balances
      setAmountIn('');
      setAmountOut('');
      setRecipientAddress('');
      refetchEthBalance();
      refetchUbaBalance();
      refetchAllowance();
    }
  }, [swapHash, isSwapWaiting]);

  // Get current balances
  const getCurrentBalance = (token) => {
    if (token === 'ETH') {
      return ethBalance ? formatEther(ethBalance.value) : '0';
    } else {
      return ubaBalance ? formatEther(ubaBalance.value) : '0';
    }
  };

  // Check if approval is needed
  useEffect(() => {
    if (tokenIn === 'UBA' && amountIn && allowance) {
      const amountInWei = parseEther(amountIn);
      setNeedsApproval(allowance < amountInWei);
    } else {
      setNeedsApproval(false);
    }
  }, [tokenIn, amountIn, allowance]);

  // Update loading state
  useEffect(() => {
    const isLoading = isApproveLoading || isSwapLoading || isApproveWaiting || isSwapWaiting;
    setIsLoading?.(isLoading);
  }, [isApproveLoading, isSwapLoading, isApproveWaiting, isSwapWaiting, setIsLoading]);

  // Handle amount input change
  const handleAmountChange = (value) => {
    setAmountIn(value);

    // Simple price calculation (mock)
    if (value && parseFloat(value) > 0) {
      let estimated;
      if (tokenIn === 'ETH' && tokenOut === 'UBA') {
        estimated = parseFloat(value) * 1000; // 1 ETH = 1000 UBA
      } else if (tokenIn === 'UBA' && tokenOut === 'ETH') {
        estimated = parseFloat(value) / 1000; // 1000 UBA = 1 ETH
      } else {
        estimated = parseFloat(value);
      }
      setAmountOut(estimated.toFixed(6));
    } else {
      setAmountOut('');
    }
  };

  // Handle max button click
  const handleMaxClick = () => {
    const balance = getCurrentBalance(tokenIn);
    let maxAmount = parseFloat(balance);

    // Leave some ETH for gas
    if (tokenIn === 'ETH') {
      maxAmount = Math.max(0, maxAmount - 0.01);
    }

    handleAmountChange(maxAmount.toString());
  };

  // Handle token swap
  const handleSwapTokens = () => {
    const tempToken = tokenIn;
    setTokenIn(tokenOut);
    setTokenOut(tempToken);
    setAmountIn('');
    setAmountOut('');
  };

  // Set recipient presets
  const setRecipientPreset = (type) => {
    switch (type) {
      case 'self':
        setRecipientAddress(address || '');
        break;
      case 'saved':
        // For demo purposes, use a placeholder
        setRecipientAddress('0x742d35Cc6634C0532925a3b8D');
        break;
      default:
        setRecipientAddress('');
    }
  };

  // Validate swap & forward
  const validateSwapForward = () => {
    if (!isConnected) {
      return { isValid: false, error: MESSAGES.ERRORS.WALLET_NOT_CONNECTED };
    }

    if (!isCorrectNetwork) {
      return { isValid: false, error: MESSAGES.ERRORS.WRONG_NETWORK };
    }

    if (!amountIn || parseFloat(amountIn) <= 0) {
      return { isValid: false, error: MESSAGES.ERRORS.INVALID_AMOUNT };
    }

    const balance = parseFloat(getCurrentBalance(tokenIn));
    if (parseFloat(amountIn) > balance) {
      return { isValid: false, error: MESSAGES.ERRORS.INSUFFICIENT_BALANCE };
    }

    if (tokenIn === tokenOut) {
      return { isValid: false, error: 'Cannot swap same tokens' };
    }

    const finalRecipient = useCustomRecipient ? recipientAddress : address;
    if (!finalRecipient || !isAddress(finalRecipient)) {
      return { isValid: false, error: MESSAGES.ERRORS.INVALID_ADDRESS };
    }

    return { isValid: true };
  };

  // Handle token approval
  const handleApprove = async () => {
    if (!amountIn) return;

    try {
      const amountInWei = parseEther(amountIn);

      approveWrite({
        address: CONTRACT_ADDRESSES.UBA_TOKEN,
        abi: CONTRACT_ABIS.UBA_TOKEN,
        functionName: 'approve',
        args: [CONTRACT_ADDRESSES.SWAP_AND_FORWARD, amountInWei],
      });
    } catch (error) {
      console.error('Approval error:', error);
      showToast({
        type: 'error',
        title: 'Approval Failed',
        message: error.message || 'Failed to approve token',
      });
    }
  };

  // Handle swap & forward execution
  const handleSwapForward = async () => {
    const validation = validateSwapForward();
    if (!validation.isValid) {
      showToast({
        type: 'error',
        title: 'Swap & Forward Error',
        message: validation.error,
      });
      return;
    }

    // Check if approval is needed first
    if (needsApproval) {
      await handleApprove();
      return;
    }

    try {
      const amountInWei = parseEther(amountIn);
      const deadline = Math.floor(Date.now() / 1000) + (settings.deadline * 60);
      const fee = FEE_TIERS.MEDIUM; // 0.3% pool fee
      const amountOutMin = 0; // For demo - should calculate based on slippage
      const finalRecipient = useCustomRecipient ? recipientAddress : address;

      const tokenInAddress = tokenIn === 'ETH' ?
        '******************************************' :
        CONTRACT_ADDRESSES.UBA_TOKEN;
      const tokenOutAddress = tokenOut === 'ETH' ?
        '******************************************' :
        CONTRACT_ADDRESSES.UBA_TOKEN;

      if (tokenIn === 'ETH') {
        // ETH to Token swap & forward
        swapWrite({
          address: CONTRACT_ADDRESSES.SWAP_AND_FORWARD,
          abi: CONTRACT_ABIS.SWAP_AND_FORWARD,
          functionName: 'swapAndForwardSingleHop',
          args: [
            tokenInAddress,
            amountInWei,
            tokenOutAddress,
            amountOutMin,
            finalRecipient,
            fee,
            deadline
          ],
          value: amountInWei,
        });
      } else {
        // Token to ETH/Token swap & forward
        swapWrite({
          address: CONTRACT_ADDRESSES.SWAP_AND_FORWARD,
          abi: CONTRACT_ABIS.SWAP_AND_FORWARD,
          functionName: 'swapAndForwardSingleHop',
          args: [
            tokenInAddress,
            amountInWei,
            tokenOutAddress,
            amountOutMin,
            finalRecipient,
            fee,
            deadline
          ],
        });
      }

    } catch (error) {
      console.error('Swap & Forward error:', error);
      showToast({
        type: 'error',
        title: 'Swap & Forward Failed',
        message: error.message || MESSAGES.ERRORS.TRANSACTION_FAILED,
      });
    }
  };

  // Get button text
  const getButtonText = () => {
    if (!isConnected) return 'Connect Wallet';
    if (!isCorrectNetwork) return 'Wrong Network';
    if (!amountIn || parseFloat(amountIn) <= 0) return 'Enter an amount';

    const balance = parseFloat(getCurrentBalance(tokenIn));
    if (parseFloat(amountIn) > balance) return 'Insufficient balance';

    const finalRecipient = useCustomRecipient ? recipientAddress : address;
    if (!finalRecipient || !isAddress(finalRecipient)) return 'Invalid recipient address';

    if (isApproveLoading || isApproveWaiting) return 'Approving...';
    if (isSwapLoading || isSwapWaiting) return 'Swapping & Forwarding...';

    if (needsApproval) return `Approve ${tokenIn}`;

    return `Swap & Forward ${tokenIn} → ${tokenOut}`;
  };

  const validation = validateSwapForward();

  return (
    <div className="swap-forward-page">
      <div className="page-header">
        <h1>⚡ Swap & Forward</h1>
        <p>Atomic swap and send in one transaction - save gas and time</p>
      </div>

      <div className="swap-container">
        <div className="swap-card">
          {/* Header */}
          <div className="swap-header">
            <h2>Swap & Forward</h2>
            <button
              className="settings-btn"
              onClick={() => setShowSettings(true)}
              aria-label="Open settings"
            >
              ⚙️
            </button>
          </div>

          {/* From Token */}
          <div className="token-input-group">
            <div className="token-input-header">
              <span className="token-label">From</span>
              <span className="token-balance" onClick={handleMaxClick}>
                Balance: {getCurrentBalance(tokenIn).slice(0, 8)}
              </span>
            </div>
            <div className="token-input">
              <input
                type="number"
                placeholder="0.0"
                value={amountIn}
                onChange={(e) => handleAmountChange(e.target.value)}
                className="amount-input"
              />
              <button className="token-selector" onClick={() => {
                setTokenSelectorType('in');
                setShowTokenSelector(true);
              }}>
                <span className="token-icon">{TOKENS[tokenIn].icon}</span>
                <span className="token-symbol">{TOKENS[tokenIn].symbol}</span>
                <span className="dropdown-arrow">▼</span>
              </button>
            </div>
          </div>

          {/* Swap Arrow */}
          <div className="swap-arrow-container">
            <button className="swap-arrow" onClick={handleSwapTokens}>
              ⇅
            </button>
          </div>

          {/* To Token */}
          <div className="token-input-group">
            <div className="token-input-header">
              <span className="token-label">To</span>
              <span className="token-balance">
                Balance: {getCurrentBalance(tokenOut).slice(0, 8)}
              </span>
            </div>
            <div className="token-input">
              <input
                type="number"
                placeholder="0.0"
                value={amountOut}
                readOnly
                className="amount-input"
              />
              <button className="token-selector" onClick={() => {
                setTokenSelectorType('out');
                setShowTokenSelector(true);
              }}>
                <span className="token-icon">{TOKENS[tokenOut].icon}</span>
                <span className="token-symbol">{TOKENS[tokenOut].symbol}</span>
                <span className="dropdown-arrow">▼</span>
              </button>
            </div>
          </div>

          {/* Recipient Section */}
          <div className="recipient-section">
            <div className="recipient-header">
              <label className="checkbox-label">
                <input
                  type="checkbox"
                  checked={useCustomRecipient}
                  onChange={(e) => setUseCustomRecipient(e.target.checked)}
                />
                <span>Send to different address</span>
              </label>
            </div>

            {useCustomRecipient && (
              <div className="recipient-input-group">
                <div className="recipient-presets">
                  <button
                    className="preset-btn"
                    onClick={() => setRecipientPreset('self')}
                  >
                    My Wallet
                  </button>
                  <button
                    className="preset-btn"
                    onClick={() => setRecipientPreset('saved')}
                  >
                    Saved Address
                  </button>
                </div>
                <input
                  type="text"
                  placeholder="0x... recipient address"
                  value={recipientAddress}
                  onChange={(e) => setRecipientAddress(e.target.value)}
                  className="recipient-input"
                />
              </div>
            )}

            <div className="recipient-info">
              <span>Recipient: </span>
              <span className="recipient-address">
                {useCustomRecipient ?
                  (recipientAddress || 'Enter address') :
                  `${address?.slice(0, 6)}...${address?.slice(-4)}` || 'Connect wallet'
                }
              </span>
            </div>
          </div>

          {/* Price Info */}
          {amountIn && amountOut && (
            <div className="price-info">
              <div className="price-row">
                <span>Rate</span>
                <span>
                  {tokenIn === 'ETH' ? '1 ETH = 1000 UBA' : '1000 UBA = 1 ETH'}
                </span>
              </div>
              <div className="price-row">
                <span>Protocol Fee (0.5%)</span>
                <span>{(parseFloat(amountOut) * 0.005).toFixed(6)} {tokenOut}</span>
              </div>
              <div className="price-row">
                <span>Minimum Received (Slippage: {settings.slippage}%)</span>
                <span>{(parseFloat(amountOut) * (1 - settings.slippage / 100)).toFixed(6)} {tokenOut}</span>
              </div>
              {needsApproval && (
                <div className="price-row">
                  <span>⚠️ Approval Required</span>
                  <span>First approve {tokenIn} spending</span>
                </div>
              )}
              <div className="price-row highlight">
                <span>⚡ Atomic Transaction</span>
                <span>Swap + Send in one step</span>
              </div>
            </div>
          )}

          {/* Swap & Forward Button */}
          <button
            className={`swap-btn ${!isButtonDisabled ? 'enabled' : 'disabled'}`}
            onClick={handleSwapForward}
            disabled={isButtonDisabled}
          >
            {getButtonText()}
          </button>
        </div>
      </div>

      {/* Settings Modal */}
      {showSettings && (
        <div className="modal-overlay" onClick={() => setShowSettings(false)}>
          <div className="modal-content" onClick={(e) => e.stopPropagation()}>
            <div className="modal-header">
              <h3>Settings</h3>
              <button onClick={() => setShowSettings(false)}>✕</button>
            </div>
            <div className="settings-content">
              <div className="setting-group">
                <label>Slippage Tolerance</label>
                <div className="slippage-options">
                  {[0.1, 0.5, 1.0].map(value => (
                    <button
                      key={value}
                      className={`slippage-btn ${settings.slippage === value ? 'active' : ''}`}
                      onClick={() => updateSettings?.({ slippage: value })}
                    >
                      {value}%
                    </button>
                  ))}
                </div>
              </div>
              <div className="setting-group">
                <label>Transaction Deadline</label>
                <div className="deadline-input">
                  <input
                    type="number"
                    value={settings.deadline}
                    onChange={(e) => updateSettings?.({ deadline: parseInt(e.target.value) })}
                    min="1"
                    max="4320"
                  />
                  <span>minutes</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Token Selector Modal */}
      <TokenSelector
        isOpen={showTokenSelector}
        onClose={() => setShowTokenSelector(false)}
        onSelect={(selectedToken) => {
          if (tokenSelectorType === 'in') {
            setTokenIn(selectedToken);
          } else {
            setTokenOut(selectedToken);
          }
          setAmountIn('');
          setAmountOut('');
        }}
        selectedToken={tokenSelectorType === 'in' ? tokenIn : tokenOut}
        address={address}
        isConnected={isConnected}
        isCorrectNetwork={isCorrectNetwork}
        showToast={showToast}
      />
    </div>
  );
};

export default SwapForwardPage;
