/* Swap Forward Page Styles - extends SwapPage.css */
@import './SwapPage.css';

/* Recipient Section */
.recipient-section {
  margin: var(--spacing-lg) 0;
  padding: var(--spacing-lg);
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  transition: var(--transition-normal);
}

.recipient-section:hover {
  background: rgba(255, 255, 255, 0.05);
  border-color: var(--border-hover);
}

.recipient-header {
  margin-bottom: var(--spacing-md);
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-secondary);
  transition: var(--transition-normal);
}

.checkbox-label:hover {
  color: var(--text-primary);
}

.checkbox-label input[type="checkbox"] {
  width: 18px;
  height: 18px;
  accent-color: #ff007a;
  cursor: pointer;
}

.recipient-input-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-md);
  animation: slideDown 0.3s ease-out;
}

.recipient-presets {
  display: flex;
  gap: var(--spacing-sm);
}

.preset-btn {
  flex: 1;
  padding: var(--spacing-sm) var(--spacing-md);
  background: var(--surface-dark);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  color: var(--text-secondary);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition-normal);
}

.preset-btn:hover {
  background: var(--surface-hover);
  color: var(--text-primary);
  border-color: var(--border-hover);
}

.preset-btn:active {
  background: rgba(255, 0, 122, 0.1);
  color: #ff007a;
  border-color: rgba(255, 0, 122, 0.3);
}

.recipient-input {
  width: 100%;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  padding: var(--spacing-md);
  color: var(--text-primary);
  font-size: 0.875rem;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  transition: var(--transition-normal);
}

.recipient-input:focus {
  outline: none;
  border-color: #ff007a;
  box-shadow: 0 0 0 2px rgba(255, 0, 122, 0.2);
  background: rgba(255, 255, 255, 0.08);
}

.recipient-input::placeholder {
  color: var(--text-muted);
  font-family: inherit;
}

.recipient-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-size: 0.875rem;
  padding: var(--spacing-sm) var(--spacing-md);
  background: rgba(255, 255, 255, 0.02);
  border-radius: var(--radius-sm);
  border: 1px solid rgba(255, 255, 255, 0.05);
}

.recipient-info span:first-child {
  color: var(--text-secondary);
  font-weight: 500;
}

.recipient-address {
  color: var(--text-primary);
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.8rem;
  background: rgba(255, 255, 255, 0.05);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  border: 1px solid var(--border-color);
}

/* Enhanced Price Info for Swap & Forward */
.price-row.highlight {
  background: rgba(255, 0, 122, 0.1);
  border: 1px solid rgba(255, 0, 122, 0.2);
  border-radius: var(--radius-sm);
  padding: var(--spacing-sm);
  margin: var(--spacing-sm) 0;
}

.price-row.highlight span:first-child {
  color: #ff007a;
  font-weight: 600;
}

.price-row.highlight span:last-child {
  color: #ff007a;
  font-weight: 500;
}

/* Atomic Transaction Badge */
.atomic-badge {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-xs) var(--spacing-sm);
  background: rgba(255, 0, 122, 0.1);
  color: #ff007a;
  border: 1px solid rgba(255, 0, 122, 0.2);
  border-radius: var(--radius-sm);
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Enhanced Swap Button for Swap & Forward */
.swap-forward-page .swap-btn.enabled {
  background: linear-gradient(135deg, #ff007a, #ff6b9d, #667eea);
  background-size: 200% 200%;
  animation: gradient-shift 3s ease infinite;
}

@keyframes gradient-shift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

.swap-forward-page .swap-btn.enabled:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(255, 0, 122, 0.4);
  animation-duration: 1.5s;
}

/* Success Animation */
.success-animation {
  animation: successPulse 0.6s ease-out;
}

@keyframes successPulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

/* Loading State for Recipient Input */
.recipient-input.loading {
  background: linear-gradient(90deg, 
    rgba(255, 255, 255, 0.05) 25%, 
    rgba(255, 255, 255, 0.1) 50%, 
    rgba(255, 255, 255, 0.05) 75%
  );
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
}

/* Responsive Design */
@media (max-width: 768px) {
  .recipient-section {
    padding: var(--spacing-md);
  }
  
  .recipient-presets {
    flex-direction: column;
  }
  
  .preset-btn {
    padding: var(--spacing-md);
  }
  
  .recipient-input {
    font-size: 0.8rem;
  }
  
  .recipient-info {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-xs);
  }
  
  .recipient-address {
    word-break: break-all;
    width: 100%;
  }
}

@media (max-width: 480px) {
  .checkbox-label {
    font-size: 0.8rem;
  }
  
  .recipient-input {
    padding: var(--spacing-sm);
  }
  
  .atomic-badge {
    font-size: 0.7rem;
    padding: 2px var(--spacing-xs);
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .recipient-section {
    border-width: 2px;
  }
  
  .recipient-input:focus {
    border-width: 2px;
  }
  
  .price-row.highlight {
    border-width: 2px;
  }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  .recipient-input-group {
    animation: none;
  }
  
  .swap-forward-page .swap-btn.enabled {
    animation: none;
    background: var(--primary-gradient);
  }
  
  .success-animation {
    animation: none;
  }
}
