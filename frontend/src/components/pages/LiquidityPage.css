/* Liquidity Page Styles */
.liquidity-page {
  max-width: 800px;
  margin: 0 auto;
  padding: var(--spacing-lg) 0;
}

/* Tabs */
.liquidity-tabs {
  display: flex;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-xl);
  background: var(--surface-dark);
  border-radius: var(--radius-lg);
  padding: var(--spacing-xs);
  border: 1px solid var(--border-color);
}

.tab-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md);
  background: transparent;
  border: none;
  border-radius: var(--radius-md);
  color: var(--text-secondary);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition-normal);
}

.tab-btn:hover {
  background: rgba(255, 255, 255, 0.05);
  color: var(--text-primary);
}

.tab-btn.active {
  background: var(--primary-gradient);
  color: var(--text-primary);
  box-shadow: 0 2px 8px rgba(255, 0, 122, 0.3);
}

.tab-btn span:first-child {
  font-size: 1rem;
}

/* Pair Selector */
.pair-selector {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
  padding: var(--spacing-lg);
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
}

.token-pair {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-md);
}

.pair-token {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md) var(--spacing-lg);
  background: var(--surface-dark);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  color: var(--text-primary);
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition-normal);
  min-width: 120px;
  justify-content: center;
}

.pair-token:hover {
  background: var(--surface-hover);
  border-color: var(--border-hover);
}

.pair-token .token-icon {
  font-size: 1.25rem;
}

.pair-separator {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-secondary);
}

.fee-tier {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.fee-tier label {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-secondary);
}

.fee-tier select {
  background: var(--surface-dark);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  padding: var(--spacing-sm) var(--spacing-md);
  color: var(--text-primary);
  font-size: 0.875rem;
}

.fee-tier select:focus {
  outline: none;
  border-color: #ff007a;
  box-shadow: 0 0 0 2px rgba(255, 0, 122, 0.2);
}

/* Liquidity Amounts */
.liquidity-amounts {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
}

.amount-input-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.amount-input-group label {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-secondary);
}

.input-wrapper {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  padding: var(--spacing-md);
  transition: var(--transition-normal);
}

.input-wrapper:focus-within {
  border-color: #ff007a;
  box-shadow: 0 0 0 2px rgba(255, 0, 122, 0.2);
}

.input-wrapper .input-field {
  flex: 1;
  background: transparent;
  border: none;
  color: var(--text-primary);
  font-size: 1.25rem;
  font-weight: 500;
  outline: none;
  margin-right: var(--spacing-md);
}

.input-wrapper .input-field::placeholder {
  color: var(--text-muted);
}

.max-btn {
  background: rgba(255, 0, 122, 0.2);
  color: #ff007a;
  border: 1px solid rgba(255, 0, 122, 0.3);
  border-radius: var(--radius-sm);
  padding: var(--spacing-xs) var(--spacing-sm);
  font-size: 0.75rem;
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition-normal);
}

.max-btn:hover {
  background: rgba(255, 0, 122, 0.3);
  border-color: #ff007a;
}

.balance-info {
  font-size: 0.875rem;
  color: var(--text-secondary);
  text-align: right;
}

/* Price Range */
.price-range {
  margin-bottom: var(--spacing-xl);
}

.price-range h4 {
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
}

.range-presets {
  display: flex;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-md);
}

.range-presets button {
  flex: 1;
  padding: var(--spacing-sm);
  background: var(--surface-dark);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  color: var(--text-secondary);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition-normal);
}

.range-presets button:hover {
  background: var(--surface-hover);
  color: var(--text-primary);
  border-color: var(--border-hover);
}

.range-inputs {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-md);
}

.range-input {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.range-input label {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-secondary);
}

.range-input input {
  background: var(--surface-dark);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  padding: var(--spacing-sm);
  color: var(--text-primary);
  font-size: 0.875rem;
}

.range-input input:focus {
  outline: none;
  border-color: #ff007a;
  box-shadow: 0 0 0 2px rgba(255, 0, 122, 0.2);
}

/* Responsive Design */
@media (max-width: 768px) {
  .liquidity-page {
    padding: var(--spacing-md) 0;
  }
  
  .liquidity-tabs {
    flex-direction: column;
    gap: var(--spacing-xs);
  }
  
  .tab-btn {
    padding: var(--spacing-sm);
    font-size: 0.8rem;
  }
  
  .token-pair {
    flex-direction: column;
    gap: var(--spacing-sm);
  }
  
  .pair-token {
    min-width: auto;
    width: 100%;
  }
  
  .range-presets {
    flex-direction: column;
  }
  
  .range-inputs {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .pair-selector {
    padding: var(--spacing-md);
  }
  
  .input-wrapper .input-field {
    font-size: 1rem;
  }
  
  .liquidity-amounts {
    gap: var(--spacing-md);
  }
}
