/* Fees Page Styles */
.fees-page {
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--spacing-lg) 0;
}

.fees-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: var(--spacing-xl);
  margin-top: var(--spacing-xl);
}

/* Statistics Grid */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: var(--spacing-lg);
}

.stat-item {
  text-align: center;
  padding: var(--spacing-md);
  background: rgba(255, 255, 255, 0.03);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-color);
  transition: var(--transition-normal);
}

.stat-item:hover {
  background: rgba(255, 255, 255, 0.05);
  border-color: var(--border-hover);
}

.stat-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.stat-label {
  font-size: 0.875rem;
  color: var(--text-secondary);
  font-weight: 500;
}

/* Claimable Section */
.claimable-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-lg);
  text-align: center;
}

.claimable-amount {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-sm);
}

.amount-display {
  display: flex;
  align-items: baseline;
  gap: var(--spacing-sm);
}

.amount-display .amount {
  font-size: 2rem;
  font-weight: 700;
  color: var(--text-primary);
}

.amount-display .token {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-secondary);
}

.amount-usd {
  font-size: 1rem;
  color: var(--text-muted);
}

.claim-btn {
  background: var(--primary-gradient);
  color: var(--text-primary);
  border: none;
  border-radius: var(--radius-md);
  padding: var(--spacing-md) var(--spacing-xl);
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition-normal);
  min-width: 200px;
}

.claim-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(255, 0, 122, 0.3);
}

.claim-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.claim-info {
  font-size: 0.875rem;
  color: var(--text-secondary);
  font-style: italic;
}

/* Admin Panel */
.admin-panel {
  border: 2px solid rgba(255, 0, 122, 0.2);
  background: rgba(255, 0, 122, 0.05);
}

.admin-panel h3 {
  color: #ff007a;
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.admin-actions {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.admin-action {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.admin-action label {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-primary);
}

.action-row {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  flex-wrap: wrap;
}

.action-row input,
.action-row select {
  flex: 1;
  min-width: 120px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  padding: var(--spacing-sm);
  color: var(--text-primary);
  font-size: 0.875rem;
}

.action-row input:focus,
.action-row select:focus {
  outline: none;
  border-color: #ff007a;
  box-shadow: 0 0 0 2px rgba(255, 0, 122, 0.2);
}

.input-suffix {
  font-size: 0.875rem;
  color: var(--text-secondary);
  white-space: nowrap;
}

.admin-btn {
  background: rgba(255, 0, 122, 0.2);
  color: #ff007a;
  border: 1px solid rgba(255, 0, 122, 0.3);
  border-radius: var(--radius-md);
  padding: var(--spacing-sm) var(--spacing-md);
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition-normal);
  white-space: nowrap;
}

.admin-btn:hover:not(:disabled) {
  background: rgba(255, 0, 122, 0.3);
  border-color: #ff007a;
}

.admin-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* External Links */
.external-links {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.external-link {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-md);
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  color: var(--text-secondary);
  text-decoration: none;
  transition: var(--transition-normal);
  font-size: 0.875rem;
}

.external-link:hover {
  background: rgba(255, 255, 255, 0.05);
  border-color: var(--border-hover);
  color: var(--text-primary);
  transform: translateX(4px);
}

/* Responsive Design */
@media (max-width: 768px) {
  .fees-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
  }
  
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-md);
  }
  
  .stat-value {
    font-size: 1.25rem;
  }
  
  .amount-display .amount {
    font-size: 1.5rem;
  }
  
  .action-row {
    flex-direction: column;
    align-items: stretch;
  }
  
  .action-row input,
  .action-row select {
    min-width: auto;
  }
}

@media (max-width: 480px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .claimable-section {
    gap: var(--spacing-md);
  }
  
  .claim-btn {
    min-width: auto;
    width: 100%;
  }
  
  .admin-actions {
    gap: var(--spacing-md);
  }
}
