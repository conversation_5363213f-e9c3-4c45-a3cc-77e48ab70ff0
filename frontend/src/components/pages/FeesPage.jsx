import React, { useState, useEffect } from 'react';
import { useReadContract, useWriteContract, useWaitForTransactionReceipt } from 'wagmi';
import { parseEther, formatEther } from 'viem';
import { CONTRACT_ADDRESSES, CONTRACT_ABIS, EXTERNAL_LINKS } from '../../config/contracts';
import './FeesPage.css';

const FeesPage = ({ isConnected, address, isCorrectNetwork, showToast, setIsLoading }) => {
  const [isAdmin, setIsAdmin] = useState(false);
  const [newMinAmount, setNewMinAmount] = useState('');
  const [newFeeRate, setNewFeeRate] = useState('');
  const [selectedToken, setSelectedToken] = useState('eth');

  // Contract reads for statistics
  const { data: protocolFeeBps } = useReadContract({
    address: CONTRACT_ADDRESSES.SWAP_AND_FORWARD,
    abi: CONTRACT_ABIS.SWAP_AND_FORWARD,
    functionName: 'protocolFeeBps',
    query: {
      enabled: isConnected && isCorrectNetwork,
    }
  });

  const { data: minimumProcessAmount } = useReadContract({
    address: CONTRACT_ADDRESSES.FEE_DISTRIBUTOR,
    abi: CONTRACT_ABIS.FEE_DISTRIBUTOR,
    functionName: 'minimumProcessAmount',
    query: {
      enabled: isConnected && isCorrectNetwork,
    }
  });

  const { data: collectedETHFees } = useReadContract({
    address: CONTRACT_ADDRESSES.FEE_DISTRIBUTOR,
    abi: CONTRACT_ABIS.FEE_DISTRIBUTOR,
    functionName: 'getCollectedFees',
    args: [CONTRACT_ADDRESSES.WETH],
    query: {
      enabled: isConnected && isCorrectNetwork,
    }
  });

  const { data: collectedUBAFees } = useReadContract({
    address: CONTRACT_ADDRESSES.FEE_DISTRIBUTOR,
    abi: CONTRACT_ABIS.FEE_DISTRIBUTOR,
    functionName: 'getCollectedFees',
    args: [CONTRACT_ADDRESSES.UBA_TOKEN],
    query: {
      enabled: isConnected && isCorrectNetwork,
    }
  });

  // Check if user is admin
  const { data: swapOwner } = useReadContract({
    address: CONTRACT_ADDRESSES.SWAP_AND_FORWARD,
    abi: CONTRACT_ABIS.SWAP_AND_FORWARD,
    functionName: 'owner',
    query: {
      enabled: isConnected && isCorrectNetwork,
    }
  });

  const { data: feeDistributorOwner } = useReadContract({
    address: CONTRACT_ADDRESSES.FEE_DISTRIBUTOR,
    abi: CONTRACT_ABIS.FEE_DISTRIBUTOR,
    functionName: 'owner',
    query: {
      enabled: isConnected && isCorrectNetwork,
    }
  });

  // Contract writes for admin functions
  const { data: processFeesHash, writeContract: processFeesWrite, isPending: isProcessingFees } = useWriteContract();
  const { data: updateMinHash, writeContract: updateMinWrite, isPending: isUpdatingMin } = useWriteContract();
  const { data: updateFeeHash, writeContract: updateFeeWrite, isPending: isUpdatingFee } = useWriteContract();

  // Wait for transactions
  const { isLoading: isProcessFeesWaiting } = useWaitForTransactionReceipt({
    hash: processFeesHash,
  });

  const { isLoading: isUpdateMinWaiting } = useWaitForTransactionReceipt({
    hash: updateMinHash,
  });

  const { isLoading: isUpdateFeeWaiting } = useWaitForTransactionReceipt({
    hash: updateFeeHash,
  });

  // Handle transaction success
  useEffect(() => {
    if (processFeesHash && !isProcessFeesWaiting) {
      showToast({
        type: 'success',
        title: 'Fees Processed!',
        message: 'Collected fees have been processed and UBA tokens burned.',
      });
    }
  }, [processFeesHash, isProcessFeesWaiting]);

  useEffect(() => {
    if (updateMinHash && !isUpdateMinWaiting) {
      showToast({
        type: 'success',
        title: 'Minimum Amount Updated!',
        message: 'Minimum process amount has been updated successfully.',
      });
      setNewMinAmount('');
    }
  }, [updateMinHash, isUpdateMinWaiting]);

  useEffect(() => {
    if (updateFeeHash && !isUpdateFeeWaiting) {
      showToast({
        type: 'success',
        title: 'Protocol Fee Updated!',
        message: 'Protocol fee rate has been updated successfully.',
      });
      setNewFeeRate('');
    }
  }, [updateFeeHash, isUpdateFeeWaiting]);

  // Check admin status
  useEffect(() => {
    if (address && (swapOwner || feeDistributorOwner)) {
      const isOwner = address.toLowerCase() === swapOwner?.toLowerCase() ||
                     address.toLowerCase() === feeDistributorOwner?.toLowerCase();
      setIsAdmin(isOwner);
    } else {
      setIsAdmin(false);
    }
  }, [address, swapOwner, feeDistributorOwner]);

  // Update loading state
  useEffect(() => {
    const loading = isProcessingFees || isUpdatingMin || isUpdatingFee;
    setIsLoading?.(loading);
  }, [isProcessingFees, isUpdatingMin, isUpdatingFee, setIsLoading]);

  const handleProcessFees = () => {
    const tokenAddress = selectedToken === 'eth' ? CONTRACT_ADDRESSES.WETH : CONTRACT_ADDRESSES.UBA_TOKEN;
    const feeTier = 3000; // 0.3% fee tier

    processFeesWrite({
      address: CONTRACT_ADDRESSES.FEE_DISTRIBUTOR,
      abi: CONTRACT_ABIS.FEE_DISTRIBUTOR,
      functionName: 'processFees',
      args: [tokenAddress, feeTier],
    });
  };

  const handleUpdateMinAmount = () => {
    if (!newMinAmount) return;

    try {
      const amountWei = parseEther(newMinAmount);
      updateMinWrite({
        address: CONTRACT_ADDRESSES.FEE_DISTRIBUTOR,
        abi: CONTRACT_ABIS.FEE_DISTRIBUTOR,
        functionName: 'updateMinimumProcessAmount',
        args: [amountWei],
      });
    } catch (error) {
      showToast({
        type: 'error',
        title: 'Invalid Amount',
        message: 'Please enter a valid amount in ETH.',
      });
    }
  };

  const handleUpdateFee = () => {
    if (!newFeeRate) return;

    const feeBps = parseInt(newFeeRate);
    if (feeBps < 0 || feeBps > 1000) {
      showToast({
        type: 'error',
        title: 'Invalid Fee Rate',
        message: 'Fee rate must be between 0 and 1000 BPS (0-10%).',
      });
      return;
    }

    updateFeeWrite({
      address: CONTRACT_ADDRESSES.SWAP_AND_FORWARD,
      abi: CONTRACT_ABIS.SWAP_AND_FORWARD,
      functionName: 'updateProtocolFee',
      args: [feeBps],
    });
  };

  return (
    <div className="fees-page">
      <div className="page-header">
        <h1>💰 Fees & Statistics</h1>
        <p>Protocol statistics and fee management dashboard</p>
      </div>

      {/* Protocol Statistics */}
      <div className="fees-grid">
        <div className="card">
          <h3>📊 Protocol Statistics</h3>
          <div className="stats-grid">
            <div className="stat-item">
              <div className="stat-value">
                {protocolFeeBps ? (Number(protocolFeeBps) / 100).toFixed(1) : '0.5'}%
              </div>
              <div className="stat-label">Protocol Fee Rate</div>
            </div>
            <div className="stat-item">
              <div className="stat-value">
                {collectedETHFees ? parseFloat(formatEther(collectedETHFees)).toFixed(4) : '0.0000'}
              </div>
              <div className="stat-label">ETH Fees Collected</div>
            </div>
            <div className="stat-item">
              <div className="stat-value">
                {collectedUBAFees ? parseFloat(formatEther(collectedUBAFees)).toFixed(0) : '0'}
              </div>
              <div className="stat-label">UBA Fees Collected</div>
            </div>
            <div className="stat-item">
              <div className="stat-value">
                {minimumProcessAmount ? parseFloat(formatEther(minimumProcessAmount)).toFixed(2) : '1.00'}
              </div>
              <div className="stat-label">Min Process Amount (ETH)</div>
            </div>
          </div>
        </div>

        {/* Claimable Fees for Users */}
        <div className="card">
          <h3>💎 Claimable Fees</h3>
          {isConnected && isCorrectNetwork ? (
            <div className="claimable-section">
              <div className="claimable-amount">
                <div className="amount-display">
                  <span className="amount">0.0000</span>
                  <span className="token">UBA</span>
                </div>
                <div className="amount-usd">~$0.00</div>
              </div>
              <button className="claim-btn" disabled>
                Claim All My Fees
              </button>
              <div className="claim-info">
                💡 Fees are earned by providing liquidity to pools
              </div>
            </div>
          ) : (
            <div className="status warning">
              Please connect your wallet to view claimable fees
            </div>
          )}
        </div>

        {/* Admin Panel */}
        {isAdmin && (
          <div className="card admin-panel">
            <h3>🔧 Admin Panel</h3>
            <div className="admin-actions">
              {/* Process Fees */}
              <div className="admin-action">
                <label>Process Collected Fees</label>
                <div className="action-row">
                  <select
                    value={selectedToken}
                    onChange={(e) => setSelectedToken(e.target.value)}
                  >
                    <option value="eth">ETH Fees</option>
                    <option value="uba">UBA Fees</option>
                  </select>
                  <button
                    className="admin-btn"
                    onClick={handleProcessFees}
                    disabled={isProcessingFees}
                  >
                    {isProcessingFees ? 'Processing...' : 'Process Fees'}
                  </button>
                </div>
              </div>

              {/* Update Minimum Amount */}
              <div className="admin-action">
                <label>Update Minimum Process Amount</label>
                <div className="action-row">
                  <input
                    type="number"
                    placeholder="1.0"
                    value={newMinAmount}
                    onChange={(e) => setNewMinAmount(e.target.value)}
                    step="0.1"
                    min="0"
                  />
                  <span className="input-suffix">ETH</span>
                  <button
                    className="admin-btn"
                    onClick={handleUpdateMinAmount}
                    disabled={isUpdatingMin || !newMinAmount}
                  >
                    {isUpdatingMin ? 'Updating...' : 'Update'}
                  </button>
                </div>
              </div>

              {/* Update Protocol Fee */}
              <div className="admin-action">
                <label>Update Protocol Fee Rate</label>
                <div className="action-row">
                  <input
                    type="number"
                    placeholder="50"
                    value={newFeeRate}
                    onChange={(e) => setNewFeeRate(e.target.value)}
                    min="0"
                    max="1000"
                  />
                  <span className="input-suffix">BPS (0-1000)</span>
                  <button
                    className="admin-btn"
                    onClick={handleUpdateFee}
                    disabled={!updateFeeConfig?.request || isUpdatingFee || !newFeeRate}
                  >
                    {isUpdatingFee ? 'Updating...' : 'Update'}
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* External Links */}
        <div className="card">
          <h3>🔗 External Links</h3>
          <div className="external-links">
            <a
              href={`${EXTERNAL_LINKS.ETHERSCAN_BASE}/address/${CONTRACT_ADDRESSES.UBA_TOKEN}`}
              target="_blank"
              rel="noopener noreferrer"
              className="external-link"
            >
              📊 UBA Token on Etherscan
            </a>
            <a
              href={`${EXTERNAL_LINKS.ETHERSCAN_BASE}/address/${CONTRACT_ADDRESSES.SWAP_AND_FORWARD}`}
              target="_blank"
              rel="noopener noreferrer"
              className="external-link"
            >
              🔄 SwapAndForward Contract
            </a>
            <a
              href={`${EXTERNAL_LINKS.ETHERSCAN_BASE}/address/${CONTRACT_ADDRESSES.FEE_DISTRIBUTOR}`}
              target="_blank"
              rel="noopener noreferrer"
              className="external-link"
            >
              💰 FeeDistributor Contract
            </a>
            <a
              href={EXTERNAL_LINKS.FAUCET}
              target="_blank"
              rel="noopener noreferrer"
              className="external-link"
            >
              🚰 Get Sepolia ETH
            </a>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FeesPage;
