import React, { useState, useEffect } from 'react';
import { useAccount, useBalance, useContractRead, useContractWrite, useWaitForTransaction } from 'wagmi';
import { ethers } from 'ethers';
import { SEPOLIA_TOKENS, CONTRACT_ADDRESSES, ERC20_ABI } from '../../config/tokens';
import './LiquidityPage.css';

const LiquidityPage = ({ isConnected, address, isCorrectNetwork, showToast, setIsLoading }) => {
  // State
  const [activeTab, setActiveTab] = useState('add'); // 'add', 'remove', 'positions'
  const [token0, setToken0] = useState(SEPOLIA_TOKENS.ETH);
  const [token1, setToken1] = useState(SEPOLIA_TOKENS.UBA);
  const [amount0, setAmount0] = useState('');
  const [amount1, setAmount1] = useState('');
  const [feeRate, setFeeRate] = useState(3000); // 0.3%

  // Balance hooks
  const { data: ethBalance, refetch: refetchEthBalance } = useBalance({
    address,
    query: {
      enabled: isConnected && isCorrectNetwork,
    }
  });

  const { data: ubaBalance, refetch: refetchUbaBalance } = useBalance({
    address,
    token: CONTRACT_ADDRESSES.UBA_TOKEN,
    query: {
      enabled: isConnected && isCorrectNetwork,
    }
  });

  // Contract write hooks
  const {
    data: addLiquidityHash,
    writeContract: addLiquidityWrite,
    isPending: isAddingLiquidity
  } = useWriteContract();

  // Wait for transactions
  const { isLoading: isAddLiquidityWaiting } = useWaitForTransactionReceipt({
    hash: addLiquidityHash,
  });

  // Handle transaction success
  useEffect(() => {
    if (addLiquidityHash && !isAddLiquidityWaiting) {
      showToast({
        type: 'success',
        title: 'Liquidity Added!',
        message: 'Successfully added liquidity to the pool',
      });
      setAmount0('');
      setAmount1('');
      refetchEthBalance();
      refetchUbaBalance();
    }
  }, [addLiquidityHash, isAddLiquidityWaiting]);

  // Update loading state
  useEffect(() => {
    const loading = isAddingLiquidity || isAddLiquidityWaiting;
    setIsLoading?.(loading);
  }, [isAddingLiquidity, isAddLiquidityWaiting, setIsLoading]);

  const getCurrentBalance = (token) => {
    if (token === 'ETH') {
      return ethBalance ? parseFloat(ethBalance.formatted) : 0;
    } else {
      return ubaBalance ? parseFloat(ubaBalance.formatted) : 0;
    }
  };

  const handleMaxAmount = (index) => {
    const token = selectedPair[index];
    const balance = getCurrentBalance(token);
    const maxAmount = token === 'ETH' ? Math.max(0, balance - 0.01) : balance;

    if (index === 0) {
      setAmount0(maxAmount.toString());
      if (selectedPair[0] === 'ETH' && selectedPair[1] === 'UBA') {
        setAmount1((maxAmount * 1000).toString());
      }
    } else {
      setAmount1(maxAmount.toString());
      if (selectedPair[0] === 'ETH' && selectedPair[1] === 'UBA') {
        setAmount0((maxAmount / 1000).toString());
      }
    }
  };

  // Handle add liquidity
  const handleAddLiquidity = async () => {
    if (!amount0 || !amount1 || parseFloat(amount0) <= 0 || parseFloat(amount1) <= 0) {
      showToast({
        type: 'error',
        title: 'Invalid Amount',
        message: 'Please enter valid amounts for both tokens',
      });
      return;
    }

    try {
      const token0Address = selectedPair[0] === 'ETH' ? 
        '******************************************' : 
        TOKENS[selectedPair[0]]?.address;
      const token1Address = selectedPair[1] === 'ETH' ? 
        '******************************************' : 
        TOKENS[selectedPair[1]]?.address;

      const amount0Wei = parseEther(amount0);
      const amount1Wei = parseEther(amount1);
      const deadline = BigInt(Math.floor(Date.now() / 1000) + 1200);

      addLiquidityWrite({
        address: CONTRACT_ADDRESSES.LIQUIDITY_MANAGER,
        abi: CONTRACT_ABIS.LIQUIDITY_MANAGER,
        functionName: 'addLiquidity',
        args: [
          token0Address,
          token1Address,
          FEE_TIERS.MEDIUM,
          -887220,
          887220,
          amount0Wei,
          amount1Wei,
          parseEther((parseFloat(amount0) * 0.99).toString()),
          parseEther((parseFloat(amount1) * 0.99).toString()),
          deadline
        ],
        value: selectedPair[0] === 'ETH' ? amount0Wei : (selectedPair[1] === 'ETH' ? amount1Wei : 0n),
      });
    } catch (error) {
      console.error('Add liquidity error:', error);
      showToast({
        type: 'error',
        title: 'Add Liquidity Failed',
        message: error.message || 'Failed to add liquidity',
      });
    }
  };

  const handleRemoveLiquidity = async () => {
    showToast({
      type: 'info',
      title: 'Feature Coming Soon',
      message: 'Remove liquidity feature will be available soon',
    });
  };

  const validateAddLiquidity = () => {
    if (!isConnected) {
      return { isValid: false, error: 'Please connect your wallet' };
    }
    if (!isCorrectNetwork) {
      return { isValid: false, error: 'Please switch to Sepolia testnet' };
    }
    if (!amount0 || parseFloat(amount0) <= 0) {
      return { isValid: false, error: 'Enter valid amount for first token' };
    }
    if (!amount1 || parseFloat(amount1) <= 0) {
      return { isValid: false, error: 'Enter valid amount for second token' };
    }
    
    const balance0 = getCurrentBalance(selectedPair[0]);
    const balance1 = getCurrentBalance(selectedPair[1]);
    
    if (parseFloat(amount0) > balance0) {
      return { isValid: false, error: `Insufficient ${selectedPair[0]} balance` };
    }
    if (parseFloat(amount1) > balance1) {
      return { isValid: false, error: `Insufficient ${selectedPair[1]} balance` };
    }
    
    return { isValid: true };
  };

  const validation = validateAddLiquidity();
  const isButtonDisabled = !validation.isValid || isAddingLiquidity || isAddLiquidityWaiting;

  return (
    <div className="liquidity-page">
      <div className="page-header">
        <h1>Liquidity</h1>
        <p>Add liquidity to earn fees</p>
      </div>

      <div className="liquidity-container">
        <div className="liquidity-card">
          <div className="tab-navigation">
            <button
              className={`tab-btn ${activeTab === 'add' ? 'active' : ''}`}
              onClick={() => setActiveTab('add')}
            >
              Add Liquidity
            </button>
            <button
              className={`tab-btn ${activeTab === 'remove' ? 'active' : ''}`}
              onClick={() => setActiveTab('remove')}
            >
              Remove Liquidity
            </button>
          </div>

          {activeTab === 'add' && (
            <div className="add-liquidity-content">
              <div className="token-pair-section">
                <h3>Select Pair</h3>
                <div className="token-pair-selector">
                  <button
                    className="token-selector"
                    onClick={() => {
                      setTokenSelectorType('tokenA');
                      setShowTokenSelector(true);
                    }}
                  >
                    <span className="token-icon">{TOKENS[selectedPair[0]].icon}</span>
                    <span className="token-symbol">{TOKENS[selectedPair[0]].symbol}</span>
                    <span className="dropdown-arrow">▼</span>
                  </button>
                  <span className="pair-separator">+</span>
                  <button
                    className="token-selector"
                    onClick={() => {
                      setTokenSelectorType('tokenB');
                      setShowTokenSelector(true);
                    }}
                  >
                    <span className="token-icon">{TOKENS[selectedPair[1]].icon}</span>
                    <span className="token-symbol">{TOKENS[selectedPair[1]].symbol}</span>
                    <span className="dropdown-arrow">▼</span>
                  </button>
                </div>
              </div>

              <div className="amount-inputs">
                <div className="token-input-group">
                  <div className="token-input-header">
                    <span className="token-label">{selectedPair[0]}</span>
                    <span className="token-balance" onClick={() => handleMaxAmount(0)}>
                      Balance: {getCurrentBalance(selectedPair[0]).toFixed(4)}
                    </span>
                  </div>
                  <div className="token-input">
                    <input
                      type="number"
                      placeholder="0.0"
                      value={amount0}
                      onChange={(e) => setAmount0(e.target.value)}
                      className="amount-input"
                    />
                    <span className="token-symbol">{selectedPair[0]}</span>
                  </div>
                </div>

                <div className="token-input-group">
                  <div className="token-input-header">
                    <span className="token-label">{selectedPair[1]}</span>
                    <span className="token-balance" onClick={() => handleMaxAmount(1)}>
                      Balance: {getCurrentBalance(selectedPair[1]).toFixed(4)}
                    </span>
                  </div>
                  <div className="token-input">
                    <input
                      type="number"
                      placeholder="0.0"
                      value={amount1}
                      onChange={(e) => setAmount1(e.target.value)}
                      className="amount-input"
                    />
                    <span className="token-symbol">{selectedPair[1]}</span>
                  </div>
                </div>
              </div>

              <button
                className={`liquidity-btn ${!isButtonDisabled ? 'enabled' : 'disabled'}`}
                onClick={handleAddLiquidity}
                disabled={isButtonDisabled}
              >
                {!validation.isValid ? validation.error : 
                 isAddingLiquidity || isAddLiquidityWaiting ? 'Adding Liquidity...' : 
                 'Add Liquidity'}
              </button>
            </div>
          )}

          {activeTab === 'remove' && (
            <div className="remove-liquidity-content">
              <div className="coming-soon">
                <h3>🚧 Coming Soon</h3>
                <p>Remove liquidity feature will be available in the next update.</p>
                <button className="btn-secondary" onClick={handleRemoveLiquidity}>
                  Learn More
                </button>
              </div>
            </div>
          )}
        </div>
      </div>

      <TokenSelector
        isOpen={showTokenSelector}
        onClose={() => setShowTokenSelector(false)}
        onSelect={(selectedToken) => {
          if (tokenSelectorType === 'tokenA') {
            setSelectedPair([selectedToken, selectedPair[1]]);
          } else {
            setSelectedPair([selectedPair[0], selectedToken]);
          }
          setAmount0('');
          setAmount1('');
        }}
        selectedToken={tokenSelectorType === 'tokenA' ? selectedPair[0] : selectedPair[1]}
        address={address}
        isConnected={isConnected}
        isCorrectNetwork={isCorrectNetwork}
        showToast={showToast}
      />
    </div>
  );
};

export default LiquidityPage;
