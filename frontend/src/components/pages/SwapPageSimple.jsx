import React, { useState, useEffect } from 'react';
import { useAccount, useBalance, useContractRead, useContractWrite, useWaitForTransaction } from 'wagmi';
import { ethers } from 'ethers';
import { SEPOLIA_TOKENS, isNativeToken, CONTRACT_ADDRESSES, ERC20_ABI } from '../../config/tokens';
import { useTokenBalance, useTokenPrice } from '../../hooks/useTokenBalance';
import './SwapPage.css';

const SwapPageSimple = ({ isConnected, address, isCorrectNetwork, showToast, setIsLoading }) => {
  // State
  const [tokenIn, setTokenIn] = useState(SEPOLIA_TOKENS.ETH);
  const [tokenOut, setTokenOut] = useState(SEPOLIA_TOKENS.UBA);
  const [amountIn, setAmountIn] = useState('');
  const [amountOut, setAmountOut] = useState('');
  const [slippage, setSlippage] = useState(0.5);

  // Real-time balances
  const { balance: balanceIn, refetch: refetchBalanceIn, lastUpdate: lastUpdateIn } = useTokenBalance(tokenIn);
  const { balance: balanceOut, refetch: refetchBalanceOut, lastUpdate: lastUpdateOut } = useTokenBalance(tokenOut);
  
  // Real-time prices
  const { price: priceIn, priceChange24h: changeIn, lastUpdate: priceUpdateIn } = useTokenPrice(tokenIn?.symbol);
  const { price: priceOut, priceChange24h: changeOut, lastUpdate: priceUpdateOut } = useTokenPrice(tokenOut?.symbol);

  // Contract interactions
  const { write: approveToken, data: approveHash } = useContractWrite({
    address: tokenIn?.address,
    abi: ERC20_ABI,
    functionName: 'approve',
  });

  const { write: swapTokens, data: swapHash } = useContractWrite({
    address: CONTRACT_ADDRESSES.SWAP_AND_FORWARD,
    abi: [
      'function swapETHForTokens(uint256 minAmountOut, address tokenOut, uint256 deadline) payable',
      'function swapTokensForETH(uint256 amountIn, uint256 minAmountOut, address tokenIn, uint256 deadline)',
    ],
  });

  const { isLoading: isApproveWaiting } = useWaitForTransaction({ hash: approveHash });
  const { isLoading: isSwapWaiting } = useWaitForTransaction({ hash: swapHash });

  // Check allowance
  const { data: allowance } = useContractRead({
    address: !isNativeToken(tokenIn) ? tokenIn?.address : undefined,
    abi: ERC20_ABI,
    functionName: 'allowance',
    args: [address, CONTRACT_ADDRESSES.SWAP_AND_FORWARD],
    enabled: isConnected && !isNativeToken(tokenIn),
    watch: true,
  });

  // Calculate exchange rate
  const calculateAmountOut = (amountInValue) => {
    if (!amountInValue || !priceIn || !priceOut) return '0';
    const rate = priceIn / priceOut;
    const amountOutValue = parseFloat(amountInValue) * rate * (1 - slippage / 100);
    return amountOutValue.toFixed(6);
  };

  // Handle amount input change
  const handleAmountInChange = (value) => {
    setAmountIn(value);
    setAmountOut(calculateAmountOut(value));
  };

  // Handle max amount
  const handleMaxAmount = () => {
    const maxAmount = isNativeToken(tokenIn) 
      ? Math.max(0, parseFloat(balanceIn) - 0.01).toFixed(6)
      : balanceIn;
    handleAmountInChange(maxAmount);
  };

  // Handle token swap
  const handleSwapTokens = () => {
    const tempToken = tokenIn;
    setTokenIn(tokenOut);
    setTokenOut(tempToken);
    setAmountIn(amountOut);
    setAmountOut(amountIn);
  };

  // Check if approval is needed
  const needsApproval = () => {
    if (isNativeToken(tokenIn) || !amountIn) return false;
    const amountInWei = ethers.utils.parseEther(amountIn);
    return !allowance || allowance.lt(amountInWei);
  };

  // Handle approve
  const handleApprove = () => {
    if (!tokenIn || isNativeToken(tokenIn)) return;
    
    try {
      const amountInWei = ethers.utils.parseEther(amountIn);
      approveToken({
        args: [CONTRACT_ADDRESSES.SWAP_AND_FORWARD, amountInWei],
      });

      showToast({
        type: 'info',
        title: 'Approval Pending',
        message: 'Please confirm the approval transaction in your wallet',
      });
    } catch (error) {
      showToast({
        type: 'error',
        title: 'Approval Failed',
        message: error.message || 'Failed to approve token',
      });
    }
  };

  // Handle swap
  const handleSwap = () => {
    if (!amountIn || !tokenIn || !tokenOut) return;
    
    try {
      const amountInWei = ethers.utils.parseEther(amountIn);
      const minAmountOut = ethers.utils.parseEther((parseFloat(amountOut) * (1 - slippage / 100)).toString());
      const deadline = Math.floor(Date.now() / 1000) + 1200; // 20 minutes

      if (isNativeToken(tokenIn)) {
        // ETH to Token
        swapTokens({
          functionName: 'swapETHForTokens',
          args: [minAmountOut, tokenOut.address, deadline],
          value: amountInWei,
        });
      } else {
        // Token to ETH
        swapTokens({
          functionName: 'swapTokensForETH',
          args: [amountInWei, minAmountOut, tokenIn.address, deadline],
        });
      }

      showToast({
        type: 'info',
        title: 'Swap Pending',
        message: 'Please confirm the swap transaction in your wallet',
      });
    } catch (error) {
      showToast({
        type: 'error',
        title: 'Swap Failed',
        message: error.message || 'Failed to execute swap',
      });
    }
  };

  // Check if swap button should be disabled
  const isSwapDisabled = () => {
    if (!isConnected || !isCorrectNetwork) return true;
    if (!amountIn || parseFloat(amountIn) <= 0) return true;
    if (parseFloat(amountIn) > parseFloat(balanceIn)) return true;
    if (isApproveWaiting || isSwapWaiting) return true;
    return false;
  };

  // Get swap button text
  const getSwapButtonText = () => {
    if (!isConnected) return 'Connect Wallet';
    if (!isCorrectNetwork) return 'Switch to Sepolia';
    if (!amountIn || parseFloat(amountIn) <= 0) return 'Enter Amount';
    if (parseFloat(amountIn) > parseFloat(balanceIn)) return 'Insufficient Balance';
    if (needsApproval()) return `Approve ${tokenIn.symbol}`;
    if (isApproveWaiting) return 'Approving...';
    if (isSwapWaiting) return 'Swapping...';
    return 'Swap';
  };

  // Handle button click
  const handleButtonClick = () => {
    if (needsApproval()) {
      handleApprove();
    } else {
      handleSwap();
    }
  };

  // Update amounts when tokens change
  useEffect(() => {
    if (amountIn) {
      setAmountOut(calculateAmountOut(amountIn));
    }
  }, [tokenIn, tokenOut, priceIn, priceOut, slippage]);

  // Success notifications
  useEffect(() => {
    if (approveHash && !isApproveWaiting) {
      showToast({
        type: 'success',
        title: 'Approval Successful!',
        message: `${tokenIn.symbol} has been approved for trading`,
      });
      refetchBalanceIn();
    }
  }, [approveHash, isApproveWaiting]);

  useEffect(() => {
    if (swapHash && !isSwapWaiting) {
      showToast({
        type: 'success',
        title: 'Swap Successful!',
        message: `Successfully swapped ${amountIn} ${tokenIn.symbol} for ${amountOut} ${tokenOut.symbol}`,
      });
      setAmountIn('');
      setAmountOut('');
      refetchBalanceIn();
      refetchBalanceOut();
    }
  }, [swapHash, isSwapWaiting]);

  return (
    <div className="swap-page">
      <div className="swap-container">
        <div className="swap-header">
          <h2>Swap Tokens</h2>
          <div className="real-time-indicator">
            <span className="indicator-dot"></span>
            Real-time Data
          </div>
        </div>

        {/* Token Input */}
        <div className="token-input-container">
          <div className="token-input-header">
            <span>From</span>
            <span>Balance: {parseFloat(balanceIn).toFixed(6)} 
              <small style={{color: '#64748b', marginLeft: '4px'}}>
                (Updated: {new Date(lastUpdateIn).toLocaleTimeString()})
              </small>
            </span>
          </div>
          <div className="token-input">
            <input
              type="number"
              placeholder="0.0"
              value={amountIn}
              onChange={(e) => handleAmountInChange(e.target.value)}
            />
            <div className="token-select-container">
              <div className="token-select-button">
                <span className="token-logo">{tokenIn.logoURI}</span>
                <span className="token-symbol">{tokenIn.symbol}</span>
              </div>
              <button className="max-button" onClick={handleMaxAmount}>
                MAX
              </button>
            </div>
          </div>
          <div className="token-value">
            ${(parseFloat(amountIn || 0) * priceIn).toFixed(2)}
            <span style={{color: changeIn >= 0 ? '#10b981' : '#ef4444', marginLeft: '8px'}}>
              {changeIn >= 0 ? '+' : ''}{changeIn.toFixed(2)}%
            </span>
          </div>
        </div>

        {/* Swap Button */}
        <div className="swap-arrow-container">
          <button className="swap-arrow-button" onClick={handleSwapTokens}>
            ⇅
          </button>
        </div>

        {/* Token Output */}
        <div className="token-input-container">
          <div className="token-input-header">
            <span>To</span>
            <span>Balance: {parseFloat(balanceOut).toFixed(6)}
              <small style={{color: '#64748b', marginLeft: '4px'}}>
                (Updated: {new Date(lastUpdateOut).toLocaleTimeString()})
              </small>
            </span>
          </div>
          <div className="token-input">
            <input
              type="number"
              placeholder="0.0"
              value={amountOut}
              readOnly
            />
            <div className="token-select-button">
              <span className="token-logo">{tokenOut.logoURI}</span>
              <span className="token-symbol">{tokenOut.symbol}</span>
            </div>
          </div>
          <div className="token-value">
            ${(parseFloat(amountOut || 0) * priceOut).toFixed(2)}
            <span style={{color: changeOut >= 0 ? '#10b981' : '#ef4444', marginLeft: '8px'}}>
              {changeOut >= 0 ? '+' : ''}{changeOut.toFixed(2)}%
            </span>
          </div>
        </div>

        {/* Swap Info */}
        {amountIn && amountOut && (
          <div className="swap-info">
            <div className="swap-rate">
              1 {tokenIn.symbol} = {(parseFloat(amountOut) / parseFloat(amountIn)).toFixed(6)} {tokenOut.symbol}
            </div>
            <div className="swap-details">
              <div>Slippage: {slippage}%</div>
              <div>Minimum received: {(parseFloat(amountOut) * (1 - slippage / 100)).toFixed(6)} {tokenOut.symbol}</div>
            </div>
          </div>
        )}

        {/* Slippage Setting */}
        <div className="slippage-container">
          <label>Slippage Tolerance: {slippage}%</label>
          <input
            type="range"
            min="0.1"
            max="5"
            step="0.1"
            value={slippage}
            onChange={(e) => setSlippage(parseFloat(e.target.value))}
          />
        </div>

        {/* Swap Button */}
        <button
          className={`swap-button ${isSwapDisabled() ? 'disabled' : ''}`}
          onClick={handleButtonClick}
          disabled={isSwapDisabled()}
        >
          {getSwapButtonText()}
        </button>

        {/* Real-time Status */}
        <div className="real-time-status">
          <div>🔄 Balance Updates: Every 3s</div>
          <div>💰 Price Updates: Every 30s</div>
          <div>🌐 Network: Sepolia Testnet</div>
        </div>
      </div>
    </div>
  );
};

export default SwapPageSimple;
