import React, { useState, useEffect } from 'react';
import {
  useAccount,
  useBalance,
  useContractRead,
  useContractWrite,
  useWaitForTransaction,
} from 'wagmi';
import { parseEther, formatEther, parseUnits, formatUnits } from 'viem';
import { SEPOLIA_TOKENS, isNativeToken, parseTokenAmount, formatTokenAmount, CONTRACT_ADDRESSES, ERC20_ABI } from '../../config/tokens';
import { useTokenBalance, useTokenPrice } from '../../hooks/useTokenBalance';
import TokenSelectorAdvanced from '../common/TokenSelectorAdvanced';
import './SwapPage.css';

const SwapPageAdvanced = ({ isConnected, address, isCorrectNetwork, settings, showToast, setIsLoading }) => {
  // State
  const [tokenIn, setTokenIn] = useState(SEPOLIA_TOKENS.ETH);
  const [tokenOut, setTokenOut] = useState(SEPOLIA_TOKENS.UBA);
  const [amountIn, setAmountIn] = useState('');
  const [amountOut, setAmountOut] = useState('');
  const [isTokenSelectorOpen, setIsTokenSelectorOpen] = useState(false);
  const [selectingToken, setSelectingToken] = useState(null); // 'in' or 'out'
  const [slippage, setSlippage] = useState(settings?.slippage || 0.5);
  const [deadline, setDeadline] = useState(settings?.deadline || 20);
  const [showSettings, setShowSettings] = useState(false);

  // Hooks
  const { balance: balanceIn, refetch: refetchBalanceIn } = useTokenBalance(tokenIn);
  const { balance: balanceOut, refetch: refetchBalanceOut } = useTokenBalance(tokenOut);
  const { price: priceIn } = useTokenPrice(tokenIn?.symbol);
  const { price: priceOut } = useTokenPrice(tokenOut?.symbol);

  // Contract interactions
  const { write: approveToken, data: approveHash } = useContractWrite({
    address: tokenIn?.address,
    abi: ERC20_ABI,
    functionName: 'approve',
  });

  const { write: swapTokens, data: swapHash } = useContractWrite({
    address: CONTRACT_ADDRESSES.SWAP_AND_FORWARD,
    abi: [
      'function swapETHForTokens(uint256 minAmountOut, address tokenOut, uint256 deadline) payable',
      'function swapTokensForETH(uint256 amountIn, uint256 minAmountOut, address tokenIn, uint256 deadline)',
      'function swapTokensForTokens(uint256 amountIn, uint256 minAmountOut, address tokenIn, address tokenOut, uint256 deadline)',
    ],
  });

  const { isLoading: isApproveWaiting } = useWaitForTransaction({
    hash: approveHash,
  });

  const { isLoading: isSwapWaiting } = useWaitForTransaction({
    hash: swapHash,
  });

  // Check allowance for ERC20 tokens
  const { data: allowance } = useContractRead({
    address: !isNativeToken(tokenIn) ? tokenIn?.address : undefined,
    abi: ERC20_ABI,
    functionName: 'allowance',
    args: [address, CONTRACT_ADDRESSES.SWAP_AND_FORWARD],
    enabled: isConnected && !isNativeToken(tokenIn),
    watch: true,
  });

  // Calculate exchange rate (mock implementation)
  const calculateAmountOut = (amountInValue) => {
    if (!amountInValue || !tokenIn || !tokenOut) return '0';
    
    // Mock exchange rate calculation
    const rate = priceIn / priceOut;
    const amountOutValue = parseFloat(amountInValue) * rate * (1 - slippage / 100);
    return amountOutValue.toFixed(6);
  };

  // Handle amount input change
  const handleAmountInChange = (value) => {
    setAmountIn(value);
    setAmountOut(calculateAmountOut(value));
  };

  // Handle token selection
  const handleTokenSelect = (token) => {
    if (selectingToken === 'in') {
      if (token.address === tokenOut.address) {
        // Swap tokens if selecting the same token
        setTokenOut(tokenIn);
      }
      setTokenIn(token);
    } else if (selectingToken === 'out') {
      if (token.address === tokenIn.address) {
        // Swap tokens if selecting the same token
        setTokenIn(tokenOut);
      }
      setTokenOut(token);
    }
    setIsTokenSelectorOpen(false);
    setSelectingToken(null);
  };

  // Handle token swap
  const handleSwapTokens = () => {
    const tempToken = tokenIn;
    setTokenIn(tokenOut);
    setTokenOut(tempToken);
    setAmountIn(amountOut);
    setAmountOut(amountIn);
  };

  // Handle max amount
  const handleMaxAmount = () => {
    const maxAmount = isNativeToken(tokenIn) 
      ? Math.max(0, parseFloat(balanceIn) - 0.01).toFixed(6) // Leave some ETH for gas
      : balanceIn;
    handleAmountInChange(maxAmount);
  };

  // Check if approval is needed
  const needsApproval = () => {
    if (isNativeToken(tokenIn) || !amountIn) return false;
    const amountInWei = parseTokenAmount(amountIn, tokenIn.decimals);
    return !allowance || allowance.toString() < amountInWei;
  };

  // Handle approve
  const handleApprove = async () => {
    if (!tokenIn || isNativeToken(tokenIn)) return;

    setIsLoading(true);
    try {
      const amountInWei = parseTokenAmount(amountIn, tokenIn.decimals);

      approveToken({
        args: [CONTRACT_ADDRESSES.SWAP_AND_FORWARD, amountInWei],
      });

      showToast({
        type: 'info',
        title: 'Approval Pending',
        message: 'Please confirm the approval transaction in your wallet',
      });
    } catch (error) {
      showToast({
        type: 'error',
        title: 'Approval Failed',
        message: error.message || 'Failed to approve token',
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Handle swap
  const handleSwap = async () => {
    if (!amountIn || !tokenIn || !tokenOut) return;

    setIsLoading(true);
    try {
      const amountInWei = parseTokenAmount(amountIn, tokenIn.decimals);
      const minAmountOut = parseTokenAmount(
        (parseFloat(amountOut) * (1 - slippage / 100)).toString(),
        tokenOut.decimals
      );

      const deadlineTimestamp = Math.floor(Date.now() / 1000) + deadline * 60;

      if (isNativeToken(tokenIn)) {
        // ETH to Token
        swapTokens({
          functionName: 'swapETHForTokens',
          args: [minAmountOut, tokenOut.address, deadlineTimestamp],
          value: amountInWei,
        });
      } else if (isNativeToken(tokenOut)) {
        // Token to ETH
        swapTokens({
          functionName: 'swapTokensForETH',
          args: [amountInWei, minAmountOut, tokenIn.address, deadlineTimestamp],
        });
      } else {
        // Token to Token
        swapTokens({
          functionName: 'swapTokensForTokens',
          args: [amountInWei, minAmountOut, tokenIn.address, tokenOut.address, deadlineTimestamp],
        });
      }

      showToast({
        type: 'info',
        title: 'Swap Pending',
        message: 'Please confirm the swap transaction in your wallet',
      });
    } catch (error) {
      showToast({
        type: 'error',
        title: 'Swap Failed',
        message: error.message || 'Failed to execute swap',
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Check if swap button should be disabled
  const isSwapDisabled = () => {
    if (!isConnected || !isCorrectNetwork) return true;
    if (!amountIn || parseFloat(amountIn) <= 0) return true;
    if (parseFloat(amountIn) > parseFloat(balanceIn)) return true;
    if (isApproveWaiting || isSwapWaiting) return true;
    return false;
  };

  // Get swap button text
  const getSwapButtonText = () => {
    if (!isConnected) return 'Connect Wallet';
    if (!isCorrectNetwork) return 'Switch to Sepolia';
    if (!amountIn || parseFloat(amountIn) <= 0) return 'Enter Amount';
    if (parseFloat(amountIn) > parseFloat(balanceIn)) return 'Insufficient Balance';
    if (needsApproval()) return `Approve ${tokenIn.symbol}`;
    if (isApproveWaiting) return 'Approving...';
    if (isSwapWaiting) return 'Swapping...';
    return 'Swap';
  };

  // Handle button click
  const handleButtonClick = () => {
    if (needsApproval()) {
      handleApprove();
    } else {
      handleSwap();
    }
  };

  // Update amounts when tokens change
  useEffect(() => {
    if (amountIn) {
      setAmountOut(calculateAmountOut(amountIn));
    }
  }, [tokenIn, tokenOut, priceIn, priceOut, slippage]);

  // Success notifications
  useEffect(() => {
    if (approveHash && !isApproveWaiting) {
      showToast({
        type: 'success',
        title: 'Approval Successful!',
        message: `${tokenIn.symbol} has been approved for trading`,
      });
      refetchBalanceIn();
    }
  }, [approveHash, isApproveWaiting]);

  useEffect(() => {
    if (swapHash && !isSwapWaiting) {
      showToast({
        type: 'success',
        title: 'Swap Successful!',
        message: `Successfully swapped ${amountIn} ${tokenIn.symbol} for ${amountOut} ${tokenOut.symbol}`,
      });
      setAmountIn('');
      setAmountOut('');
      refetchBalanceIn();
      refetchBalanceOut();
    }
  }, [swapHash, isSwapWaiting]);

  return (
    <div className="swap-page">
      <div className="swap-container">
        <div className="swap-header">
          <h2>Swap Tokens</h2>
          <button 
            className="settings-button"
            onClick={() => setShowSettings(!showSettings)}
          >
            ⚙️
          </button>
        </div>

        {/* Settings Panel */}
        {showSettings && (
          <div className="settings-panel">
            <div className="setting-item">
              <label>Slippage Tolerance (%)</label>
              <input
                type="number"
                value={slippage}
                onChange={(e) => setSlippage(parseFloat(e.target.value))}
                step="0.1"
                min="0.1"
                max="50"
              />
            </div>
            <div className="setting-item">
              <label>Transaction Deadline (minutes)</label>
              <input
                type="number"
                value={deadline}
                onChange={(e) => setDeadline(parseInt(e.target.value))}
                min="1"
                max="60"
              />
            </div>
          </div>
        )}

        {/* Token Input */}
        <div className="token-input-container">
          <div className="token-input-header">
            <span>From</span>
            <span>Balance: {parseFloat(balanceIn).toFixed(6)}</span>
          </div>
          <div className="token-input">
            <input
              type="number"
              placeholder="0.0"
              value={amountIn}
              onChange={(e) => handleAmountInChange(e.target.value)}
            />
            <div className="token-select-container">
              <button
                className="token-select-button"
                onClick={() => {
                  setSelectingToken('in');
                  setIsTokenSelectorOpen(true);
                }}
              >
                <span className="token-logo">{tokenIn.logoURI}</span>
                <span className="token-symbol">{tokenIn.symbol}</span>
                <span className="dropdown-arrow">▼</span>
              </button>
              <button className="max-button" onClick={handleMaxAmount}>
                MAX
              </button>
            </div>
          </div>
          <div className="token-value">
            ${(parseFloat(amountIn || 0) * priceIn).toFixed(2)}
          </div>
        </div>

        {/* Swap Button */}
        <div className="swap-arrow-container">
          <button className="swap-arrow-button" onClick={handleSwapTokens}>
            ⇅
          </button>
        </div>

        {/* Token Output */}
        <div className="token-input-container">
          <div className="token-input-header">
            <span>To</span>
            <span>Balance: {parseFloat(balanceOut).toFixed(6)}</span>
          </div>
          <div className="token-input">
            <input
              type="number"
              placeholder="0.0"
              value={amountOut}
              readOnly
            />
            <button
              className="token-select-button"
              onClick={() => {
                setSelectingToken('out');
                setIsTokenSelectorOpen(true);
              }}
            >
              <span className="token-logo">{tokenOut.logoURI}</span>
              <span className="token-symbol">{tokenOut.symbol}</span>
              <span className="dropdown-arrow">▼</span>
            </button>
          </div>
          <div className="token-value">
            ${(parseFloat(amountOut || 0) * priceOut).toFixed(2)}
          </div>
        </div>

        {/* Swap Info */}
        {amountIn && amountOut && (
          <div className="swap-info">
            <div className="swap-rate">
              1 {tokenIn.symbol} = {(parseFloat(amountOut) / parseFloat(amountIn)).toFixed(6)} {tokenOut.symbol}
            </div>
            <div className="swap-details">
              <div>Slippage: {slippage}%</div>
              <div>Minimum received: {(parseFloat(amountOut) * (1 - slippage / 100)).toFixed(6)} {tokenOut.symbol}</div>
            </div>
          </div>
        )}

        {/* Swap Button */}
        <button
          className={`swap-button ${isSwapDisabled() ? 'disabled' : ''}`}
          onClick={handleButtonClick}
          disabled={isSwapDisabled()}
        >
          {getSwapButtonText()}
        </button>
      </div>

      {/* Token Selector Modal */}
      <TokenSelectorAdvanced
        isOpen={isTokenSelectorOpen}
        onClose={() => {
          setIsTokenSelectorOpen(false);
          setSelectingToken(null);
        }}
        onSelect={handleTokenSelect}
        selectedToken={selectingToken === 'in' ? tokenIn : tokenOut}
        showToast={showToast}
      />
    </div>
  );
};

export default SwapPageAdvanced;
