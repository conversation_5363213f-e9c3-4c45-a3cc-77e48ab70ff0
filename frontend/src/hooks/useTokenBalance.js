import { useState, useEffect } from 'react';
import { useAccount, useBalance, useContractRead } from 'wagmi';
import { isNativeToken, ERC20_ABI, formatTokenAmount } from '../config/tokens';

// RPC endpoints for real-time data
const RPC_ENDPOINTS = {
  sepolia: 'https://eth-sepolia.g.alchemy.com/v2/********************************',
  etherscan: 'https://api-sepolia.etherscan.io/api',
};

// Real-time balance fetcher using RPC and Etherscan
const fetchBalanceRealtime = async (address, token) => {
  try {
    if (isNativeToken(token)) {
      // Fetch ETH balance via RPC
      const response = await fetch(RPC_ENDPOINTS.sepolia, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          jsonrpc: '2.0',
          method: 'eth_getBalance',
          params: [address, 'latest'],
          id: 1,
        }),
      });
      const data = await response.json();
      if (data.result) {
        return formatTokenAmount(parseInt(data.result, 16).toString(), 18);
      }
    } else {
      // Fetch ERC20 balance via Etherscan API
      const response = await fetch(
        `${RPC_ENDPOINTS.etherscan}?module=account&action=tokenbalance&contractaddress=${token.address}&address=${address}&tag=latest&apikey=YourApiKeyToken`
      );
      const data = await response.json();
      if (data.status === '1') {
        return formatTokenAmount(data.result, token.decimals);
      }
    }
    return '0';
  } catch (error) {
    console.error('Error fetching balance:', error);
    return '0';
  }
};

export const useTokenBalance = (token) => {
  const { address, isConnected } = useAccount();
  const [balance, setBalance] = useState('0');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [lastUpdate, setLastUpdate] = useState(Date.now());

  // Fallback wagmi hooks for immediate data
  const { data: ethBalance, refetch: refetchEth } = useBalance({
    address,
    enabled: isConnected && isNativeToken(token),
    watch: false,
  });

  const { data: tokenBalance, refetch: refetchToken } = useContractRead({
    address: token?.address,
    abi: ERC20_ABI,
    functionName: 'balanceOf',
    args: [address],
    enabled: isConnected && !isNativeToken(token) && !!token?.address,
    watch: false,
  });

  // Real-time balance fetching
  useEffect(() => {
    if (!isConnected || !token || !address) {
      setBalance('0');
      setIsLoading(false);
      setError(null);
      return;
    }

    const fetchBalance = async () => {
      setIsLoading(true);
      try {
        const realtimeBalance = await fetchBalanceRealtime(address, token);
        setBalance(realtimeBalance);
        setError(null);
        setLastUpdate(Date.now());
      } catch (err) {
        setError(err.message);
        // Fallback to wagmi data
        if (isNativeToken(token) && ethBalance) {
          setBalance(formatTokenAmount(ethBalance.value.toString(), 18));
        } else if (tokenBalance) {
          setBalance(formatTokenAmount(tokenBalance.toString(), token.decimals));
        }
      } finally {
        setIsLoading(false);
      }
    };

    // Initial fetch
    fetchBalance();

    // Set up real-time updates every 3 seconds
    const interval = setInterval(fetchBalance, 3000);

    return () => clearInterval(interval);
  }, [isConnected, token, address, ethBalance, tokenBalance]);

  const refetch = async () => {
    if (!isConnected || !token || !address) return;

    setIsLoading(true);
    try {
      const realtimeBalance = await fetchBalanceRealtime(address, token);
      setBalance(realtimeBalance);
      setLastUpdate(Date.now());
    } catch (err) {
      // Fallback to wagmi refetch
      if (isNativeToken(token)) {
        refetchEth();
      } else {
        refetchToken();
      }
    } finally {
      setIsLoading(false);
    }
  };

  return {
    balance,
    isLoading,
    error,
    refetch,
    lastUpdate,
    formattedBalance: parseFloat(balance).toFixed(6),
    hasBalance: parseFloat(balance) > 0,
  };
};

// Hook for multiple token balances
export const useMultipleTokenBalances = (tokens) => {
  const { address, isConnected } = useAccount();
  const [balances, setBalances] = useState({});
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (!isConnected || !tokens || tokens.length === 0) {
      setBalances({});
      setIsLoading(false);
      return;
    }

    setIsLoading(true);

    const fetchBalances = async () => {
      const newBalances = {};
      
      for (const token of tokens) {
        try {
          if (isNativeToken(token)) {
            // Fetch ETH balance
            const response = await fetch(`https://api.etherscan.io/api?module=account&action=balance&address=${address}&tag=latest&apikey=YourApiKeyToken`);
            const data = await response.json();
            if (data.status === '1') {
              newBalances[token.symbol] = formatTokenAmount(data.result, 18);
            }
          } else {
            // This would be handled by individual useContractRead hooks
            newBalances[token.symbol] = '0';
          }
        } catch (error) {
          console.error(`Error fetching balance for ${token.symbol}:`, error);
          newBalances[token.symbol] = '0';
        }
      }

      setBalances(newBalances);
      setIsLoading(false);
    };

    fetchBalances();
  }, [isConnected, tokens, address]);

  return {
    balances,
    isLoading,
    getBalance: (symbol) => balances[symbol] || '0',
  };
};

// Hook for token allowance
export const useTokenAllowance = (token, spender) => {
  const { address, isConnected } = useAccount();

  const { data: allowance, isLoading, refetch } = useContractRead({
    address: token?.address,
    abi: ERC20_ABI,
    functionName: 'allowance',
    args: [address, spender],
    enabled: isConnected && !isNativeToken(token) && !!token?.address && !!spender,
    watch: true,
    cacheTime: 2000,
  });

  return {
    allowance: allowance ? allowance.toString() : '0',
    isLoading,
    refetch,
    hasAllowance: allowance && allowance.gt(0),
    formattedAllowance: allowance ? formatTokenAmount(allowance.toString(), token?.decimals) : '0',
  };
};

// Real-time price fetcher using CoinGecko API
const fetchPriceRealtime = async (tokenSymbol) => {
  try {
    // Map token symbols to CoinGecko IDs
    const coinGeckoIds = {
      ETH: 'ethereum',
      WETH: 'ethereum',
      UBA: 'ethereum', // Fallback to ETH for custom tokens
      USDC: 'usd-coin',
      USDT: 'tether',
      DAI: 'dai',
      LINK: 'chainlink',
      UNI: 'uniswap',
      AAVE: 'aave',
      COMP: 'compound-governance-token',
      MATIC: 'matic-network',
      SHIB: 'shiba-inu',
    };

    const coinId = coinGeckoIds[tokenSymbol] || 'ethereum';

    // Use CoinGecko API for real prices
    const response = await fetch(
      `https://api.coingecko.com/api/v3/simple/price?ids=${coinId}&vs_currencies=usd&include_24hr_change=true`,
      {
        headers: { 'Accept': 'application/json' },
        mode: 'cors'
      }
    );

    if (response.ok) {
      const data = await response.json();
      const coinData = data[coinId];
      if (coinData) {
        return {
          price: coinData.usd || 0,
          change24h: coinData.usd_24h_change || 0,
        };
      }
    }

    // Fallback to mock data if API fails
    const mockPrices = {
      ETH: { price: 2000, change24h: 2.5 },
      UBA: { price: 0.1, change24h: -1.2 },
      USDC: { price: 1.0, change24h: 0.1 },
      USDT: { price: 1.0, change24h: -0.05 },
      DAI: { price: 1.0, change24h: 0.02 },
      WETH: { price: 2000, change24h: 2.5 },
      LINK: { price: 15.5, change24h: 3.2 },
      UNI: { price: 6.8, change24h: -0.8 },
      AAVE: { price: 85.2, change24h: 1.9 },
      COMP: { price: 45.6, change24h: -2.1 },
      MATIC: { price: 0.85, change24h: 4.2 },
      SHIB: { price: 0.000008, change24h: -5.1 },
    };

    return mockPrices[tokenSymbol] || { price: 0, change24h: 0 };
  } catch (error) {
    console.error('Error fetching price:', error);
    return { price: 0, change24h: 0 };
  }
};

// Hook for real-time price data
export const useTokenPrice = (tokenSymbol) => {
  const [price, setPrice] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [priceChange24h, setPriceChange24h] = useState(0);
  const [lastUpdate, setLastUpdate] = useState(Date.now());

  useEffect(() => {
    if (!tokenSymbol) return;

    const fetchPrice = async () => {
      setIsLoading(true);
      try {
        const priceData = await fetchPriceRealtime(tokenSymbol);
        setPrice(priceData.price);
        setPriceChange24h(priceData.change24h);
        setLastUpdate(Date.now());
      } catch (error) {
        console.error('Price fetch error:', error);
      } finally {
        setIsLoading(false);
      }
    };

    // Initial fetch
    fetchPrice();

    // Set up real-time updates every 30 seconds for prices
    const interval = setInterval(fetchPrice, 30000);

    return () => clearInterval(interval);
  }, [tokenSymbol]);

  return {
    price,
    priceChange24h,
    isLoading,
    lastUpdate,
    formattedPrice: price.toFixed(price < 1 ? 8 : 2),
    priceChangeColor: priceChange24h >= 0 ? '#10b981' : '#ef4444',
  };
};
