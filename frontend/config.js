// UbaSwap Configuration
const CONFIG = {
    // Network Configuration
    SEPOLIA_CHAIN_ID: '0xaa36a7', // 11155111 in hex
    NETWORK_NAME: 'Sepolia',

    // Contract Addresses on Sepolia
    CONTRACTS: {
        UBA_TOKEN: '******************************************',
        SWAP_AND_FORWARD: '******************************************',
        FEE_DISTRIBUTOR: '******************************************',
        WETH: '******************************************',
        UNISWAP_ROUTER: '******************************************',
        UNISWAP_QUOTER: '******************************************'
    },

    // Token Configuration
    TOKENS: {
        ETH: {
            address: '******************************************',
            symbol: 'ETH',
            name: 'Ethereum',
            icon: 'Ξ',
            decimals: 18,
            color: '#627eea',
            isNative: true
        },
        UBA: {
            address: '******************************************',
            symbol: 'UBA',
            name: 'UBA Token',
            icon: '🦄',
            decimals: 18,
            color: '#ff007a',
            isNative: false
        },
        WETH: {
            address: '******************************************',
            symbol: 'WETH',
            name: 'Wrapped Ether',
            icon: '🔄',
            decimals: 18,
            color: '#627eea',
            isNative: false
        },
        USDC: {
            address: '******************************************',
            symbol: 'USDC',
            name: 'USD Coin',
            icon: '💵',
            decimals: 6,
            color: '#2775ca',
            isNative: false
        },
        USDT: {
            address: '******************************************',
            symbol: 'USDT',
            name: 'Tether USD',
            icon: '💰',
            decimals: 6,
            color: '#26a17b',
            isNative: false
        },
        DAI: {
            address: '******************************************',
            symbol: 'DAI',
            name: 'Dai Stablecoin',
            icon: '🟡',
            decimals: 18,
            color: '#f5ac37',
            isNative: false
        },
        // Token trending di Sepolia
        LINK: {
            address: '******************************************',
            symbol: 'LINK',
            name: 'Chainlink Token',
            icon: '🔗',
            decimals: 18,
            color: '#375bd2',
            isNative: false
        },
        UNI: {
            address: '******************************************',
            symbol: 'UNI',
            name: 'Uniswap Token',
            icon: '🦄',
            decimals: 18,
            color: '#ff007a',
            isNative: false
        },
        AAVE: {
            address: '0x88541670E55cC00bEEFD87eB59EDd1b7C511AC9a',
            symbol: 'AAVE',
            name: 'Aave Token',
            icon: '👻',
            decimals: 18,
            color: '#b6509e',
            isNative: false
        },
        MATIC: {
            address: '0x499d11E0b6eAC7c0593d8Fb292DCBbF815Fb29Ae',
            symbol: 'MATIC',
            name: 'Polygon Token',
            icon: '🔷',
            decimals: 18,
            color: '#8247e5',
            isNative: false
        },
        CRV: {
            address: '0xf939E0A03FB07F59A73314E73794Be0E57ac1b4E',
            symbol: 'CRV',
            name: 'Curve DAO Token',
            icon: '🌀',
            decimals: 18,
            color: '#40649a',
            isNative: false
        }
    },

    // Protocol Configuration
    PROTOCOL: {
        FEE_BPS: 50, // 0.5%
        MAX_FEE_BPS: 1000, // 10%
        BASIS_POINTS: 10000,
        DEFAULT_SLIPPAGE: 0.5, // 0.5%
        DEFAULT_DEADLINE: 20, // 20 minutes
        MIN_PROCESS_AMOUNT: '1000000000000000000' // 1 ETH in wei
    },

    // Uniswap V3 Fee Tiers
    FEE_TIERS: {
        LOW: 500,    // 0.05%
        MEDIUM: 3000, // 0.3%
        HIGH: 10000   // 1%
    },

    // UI Configuration
    UI: {
        REFRESH_INTERVAL: 30000, // 30 seconds
        PRICE_UPDATE_INTERVAL: 5000, // 5 seconds
        BALANCE_DECIMALS: 4,
        AMOUNT_DECIMALS: 6
    },

    // External Links
    LINKS: {
        ETHERSCAN_BASE: 'https://sepolia.etherscan.io',
        FAUCET: 'https://sepoliafaucet.com',
        DOCS: 'https://docs.uniswap.org',
        GITHUB: 'https://github.com/ubaswap'
    },

    // Error Messages
    ERRORS: {
        WALLET_NOT_CONNECTED: 'Silakan hubungkan dompet Anda terlebih dahulu',
        WRONG_NETWORK: 'Silakan beralih ke jaringan Sepolia',
        INSUFFICIENT_BALANCE: 'Saldo tidak mencukupi',
        INVALID_AMOUNT: 'Jumlah tidak valid',
        INVALID_ADDRESS: 'Alamat tidak valid',
        TRANSACTION_FAILED: 'Transaksi gagal',
        USER_REJECTED: 'Transaksi ditolak oleh pengguna',
        SLIPPAGE_TOO_HIGH: 'Slippage terlalu tinggi'
    },

    // Success Messages
    SUCCESS: {
        WALLET_CONNECTED: 'Dompet berhasil terhubung!',
        TRANSACTION_SENT: 'Transaksi berhasil dikirim!',
        SWAP_COMPLETED: 'Swap berhasil diselesaikan!',
        LIQUIDITY_ADDED: 'Likuiditas berhasil ditambahkan!',
        LIQUIDITY_REMOVED: 'Likuiditas berhasil dihapus!',
        FEES_CLAIMED: 'Fee berhasil diklaim!'
    }
};

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
    module.exports = CONFIG;
}

// Global access
window.CONFIG = CONFIG;
