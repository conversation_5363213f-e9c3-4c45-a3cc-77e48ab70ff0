<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>UbaSwap DEX - Decentralized Exchange</title>
    <meta name="description" content="Trade tokens instantly with low fees on UbaSwap DEX" />
    
    <!-- Favicon -->
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🦄</text></svg>" />
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Meta tags for SEO -->
    <meta property="og:title" content="UbaSwap DEX - Decentralized Exchange" />
    <meta property="og:description" content="Trade tokens instantly with low fees on UbaSwap DEX" />
    <meta property="og:type" content="website" />
    <meta property="og:image" content="/og-image.png" />
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="UbaSwap DEX" />
    <meta name="twitter:description" content="Trade tokens instantly with low fees" />
    
    <style>
      /* Loading screen styles */
      #loading-screen {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: #ffffff;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        z-index: 9999;
        color: #0f172a;
        font-family: 'Inter', sans-serif;
      }
      
      .loading-logo {
        font-size: 4rem;
        margin-bottom: 1rem;
        animation: bounce 2s infinite;
      }
      
      .loading-text {
        font-size: 1.5rem;
        font-weight: 600;
        margin-bottom: 2rem;
        background: linear-gradient(135deg, #0052ff, #0066ff);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }

      .loading-spinner {
        width: 40px;
        height: 40px;
        border: 3px solid rgba(0, 0, 0, 0.1);
        border-top: 3px solid #0052ff;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }

      .loading-status {
        margin-top: 1rem;
        font-size: 0.875rem;
        color: rgba(0, 0, 0, 0.6);
      }
      
      @keyframes bounce {
        0%, 20%, 50%, 80%, 100% {
          transform: translateY(0);
        }
        40% {
          transform: translateY(-10px);
        }
        60% {
          transform: translateY(-5px);
        }
      }
      
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
      
      /* Hide loading screen when app loads */
      .app-loaded #loading-screen {
        opacity: 0;
        pointer-events: none;
        transition: opacity 0.5s ease-out;
      }
      
      /* Base styles */
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }
      
      body {
        font-family: 'Inter', sans-serif;
        background: #ffffff;
        color: #0f172a;
        min-height: 100vh;
        overflow-x: hidden;
      }
      
      #root {
        min-height: 100vh;
      }
    </style>
  </head>
  <body>
    <!-- Loading Screen -->
    <div id="loading-screen">
      <div class="loading-logo">🦄</div>
      <div class="loading-text">UbaSwap DEX</div>
      <div class="loading-spinner"></div>
      <div class="loading-status">Loading decentralized exchange...</div>
    </div>
    
    <!-- React App Root -->
    <div id="root"></div>
    
    <!-- Vite Script -->
    <script type="module" src="/src/main.jsx"></script>
    
    <!-- Loading Script -->
    <script>
      // Hide loading screen when React app loads
      function hideLoadingScreen() {
        const loadingScreen = document.getElementById('loading-screen');
        if (loadingScreen) {
          loadingScreen.style.opacity = '0';
          setTimeout(() => {
            loadingScreen.remove();
          }, 300);
        }
      }

      // Check if React app has loaded
      function checkAppLoaded() {
        const root = document.getElementById('root');
        if (root && root.children.length > 0) {
          hideLoadingScreen();
        } else {
          setTimeout(checkAppLoaded, 100);
        }
      }

      // Start checking after DOM is loaded
      document.addEventListener('DOMContentLoaded', () => {
        setTimeout(checkAppLoaded, 500);
      });

      // Fallback: hide loading screen after 10 seconds
      setTimeout(() => {
        hideLoadingScreen();
      }, 10000);

      // Error handling
      window.addEventListener('error', (e) => {
        console.error('Application error:', e.error);
        const loadingStatus = document.querySelector('.loading-status');
        if (loadingStatus) {
          loadingStatus.textContent = 'Error loading application. Please refresh.';
          loadingStatus.style.color = '#ef4444';
        }
      });
    </script>
  </body>
</html>
