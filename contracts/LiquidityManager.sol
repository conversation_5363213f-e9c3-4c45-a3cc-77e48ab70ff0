// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol";
import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/utils/ReentrancyGuard.sol";
import "./interfaces/INonfungiblePositionManager.sol";
import "./interfaces/IWETH.sol";

/**
 * @title LiquidityManager
 * @dev Kontrak untuk mengelola likuiditas di Uniswap V3
 * @notice Menyediakan fungsi untuk menambah dan menghapus likuiditas
 */
contract LiquidityManager is Ownable, ReentrancyGuard {
    using SafeERC20 for IERC20;

    /// @notice Uniswap V3 Position Manager
    INonfungiblePositionManager public immutable positionManager;

    /// @notice WETH contract
    IWETH public immutable WETH;

    /// @notice Fee distributor untuk mengumpulkan fee
    address public feeDistributor;

    /// @notice Protocol fee dalam basis points (1 BPS = 0.01%)
    uint256 public protocolFeeBps = 50; // 0.5%

    /// @notice Basis points untuk perhitungan fee
    uint256 public constant BASIS_POINTS = 10000;

    /// @notice Struktur untuk menyimpan informasi posisi
    struct PositionInfo {
        uint256 tokenId;
        address token0;
        address token1;
        uint24 fee;
        int24 tickLower;
        int24 tickUpper;
        uint128 liquidity;
        address owner;
    }

    /// @notice Mapping dari tokenId ke informasi posisi
    mapping(uint256 => PositionInfo) public positions;

    /// @notice Mapping dari owner ke array tokenId
    mapping(address => uint256[]) public userPositions;

    /// @notice Event untuk penambahan likuiditas
    event LiquidityAdded(
        address indexed user,
        uint256 indexed tokenId,
        address token0,
        address token1,
        uint24 fee,
        uint128 liquidity,
        uint256 amount0,
        uint256 amount1
    );

    /// @notice Event untuk penghapusan likuiditas
    event LiquidityRemoved(
        address indexed user,
        uint256 indexed tokenId,
        uint128 liquidity,
        uint256 amount0,
        uint256 amount1
    );

    /// @notice Event untuk pengumpulan fee
    event FeesCollected(
        address indexed user,
        uint256 indexed tokenId,
        uint256 amount0,
        uint256 amount1
    );

    /// @notice Constructor
    /// @param _positionManager Alamat Uniswap V3 Position Manager
    /// @param _weth Alamat WETH contract
    /// @param _feeDistributor Alamat fee distributor
    constructor(
        address _positionManager,
        address _weth,
        address _feeDistributor
    ) Ownable(msg.sender) {
        require(_positionManager != address(0), "LiquidityManager: position manager tidak boleh zero");
        require(_weth != address(0), "LiquidityManager: WETH tidak boleh zero");
        require(_feeDistributor != address(0), "LiquidityManager: fee distributor tidak boleh zero");

        positionManager = INonfungiblePositionManager(_positionManager);
        WETH = IWETH(_weth);
        feeDistributor = _feeDistributor;
    }

    /// @notice Menerima ETH
    receive() external payable {}

    /**
     * @notice Menambah likuiditas ke pool Uniswap V3
     * @param token0 Alamat token pertama
     * @param token1 Alamat token kedua
     * @param fee Fee tier pool
     * @param tickLower Tick bawah untuk range
     * @param tickUpper Tick atas untuk range
     * @param amount0Desired Jumlah token0 yang diinginkan
     * @param amount1Desired Jumlah token1 yang diinginkan
     * @param amount0Min Jumlah minimum token0
     * @param amount1Min Jumlah minimum token1
     * @param deadline Deadline transaksi
     * @return tokenId ID posisi NFT
     * @return liquidity Jumlah likuiditas yang ditambahkan
     * @return amount0 Jumlah aktual token0 yang digunakan
     * @return amount1 Jumlah aktual token1 yang digunakan
     */
    function addLiquidity(
        address token0,
        address token1,
        uint24 fee,
        int24 tickLower,
        int24 tickUpper,
        uint256 amount0Desired,
        uint256 amount1Desired,
        uint256 amount0Min,
        uint256 amount1Min,
        uint256 deadline
    ) external payable nonReentrant returns (
        uint256 tokenId,
        uint128 liquidity,
        uint256 amount0,
        uint256 amount1
    ) {
        require(deadline >= block.timestamp, "LiquidityManager: deadline terlewat");
        require(amount0Desired > 0 || amount1Desired > 0, "LiquidityManager: jumlah harus lebih besar dari 0");

        // Handle ETH conversion to WETH if needed
        if (token0 == address(0)) {
            require(msg.value >= amount0Desired, "LiquidityManager: ETH tidak mencukupi");
            WETH.deposit{value: amount0Desired}();
            token0 = address(WETH);
        } else if (token1 == address(0)) {
            require(msg.value >= amount1Desired, "LiquidityManager: ETH tidak mencukupi");
            WETH.deposit{value: amount1Desired}();
            token1 = address(WETH);
        }

        // Transfer tokens from user
        if (token0 != address(WETH) || msg.value == 0) {
            IERC20(token0).safeTransferFrom(msg.sender, address(this), amount0Desired);
        }
        if (token1 != address(WETH) || msg.value == 0) {
            IERC20(token1).safeTransferFrom(msg.sender, address(this), amount1Desired);
        }

        // Approve position manager
        IERC20(token0).approve(address(positionManager), amount0Desired);
        IERC20(token1).approve(address(positionManager), amount1Desired);

        // Mint position
        INonfungiblePositionManager.MintParams memory params = INonfungiblePositionManager.MintParams({
            token0: token0,
            token1: token1,
            fee: fee,
            tickLower: tickLower,
            tickUpper: tickUpper,
            amount0Desired: amount0Desired,
            amount1Desired: amount1Desired,
            amount0Min: amount0Min,
            amount1Min: amount1Min,
            recipient: address(this),
            deadline: deadline
        });

        (tokenId, liquidity, amount0, amount1) = positionManager.mint(params);

        // Store position info
        positions[tokenId] = PositionInfo({
            tokenId: tokenId,
            token0: token0,
            token1: token1,
            fee: fee,
            tickLower: tickLower,
            tickUpper: tickUpper,
            liquidity: liquidity,
            owner: msg.sender
        });

        userPositions[msg.sender].push(tokenId);

        // Refund unused tokens
        if (amount0Desired > amount0) {
            uint256 refund0 = amount0Desired - amount0;
            if (token0 == address(WETH)) {
                WETH.withdraw(refund0);
                payable(msg.sender).transfer(refund0);
            } else {
                IERC20(token0).safeTransfer(msg.sender, refund0);
            }
        }

        if (amount1Desired > amount1) {
            uint256 refund1 = amount1Desired - amount1;
            if (token1 == address(WETH)) {
                WETH.withdraw(refund1);
                payable(msg.sender).transfer(refund1);
            } else {
                IERC20(token1).safeTransfer(msg.sender, refund1);
            }
        }

        // Refund excess ETH
        if (msg.value > 0) {
            uint256 ethUsed = (token0 == address(WETH) ? amount0 : 0) +
                             (token1 == address(WETH) ? amount1 : 0);
            if (msg.value > ethUsed) {
                payable(msg.sender).transfer(msg.value - ethUsed);
            }
        }

        emit LiquidityAdded(msg.sender, tokenId, token0, token1, fee, liquidity, amount0, amount1);
    }

    /**
     * @notice Menghapus likuiditas dari posisi
     * @param tokenId ID posisi NFT
     * @param liquidity Jumlah likuiditas yang akan dihapus
     * @param amount0Min Jumlah minimum token0 yang diharapkan
     * @param amount1Min Jumlah minimum token1 yang diharapkan
     * @param deadline Deadline transaksi
     * @return amount0 Jumlah token0 yang diterima
     * @return amount1 Jumlah token1 yang diterima
     */
    function removeLiquidity(
        uint256 tokenId,
        uint128 liquidity,
        uint256 amount0Min,
        uint256 amount1Min,
        uint256 deadline
    ) external nonReentrant returns (uint256 amount0, uint256 amount1) {
        require(deadline >= block.timestamp, "LiquidityManager: deadline terlewat");
        require(positions[tokenId].owner == msg.sender, "LiquidityManager: bukan pemilik posisi");
        require(liquidity > 0, "LiquidityManager: likuiditas harus lebih besar dari 0");

        PositionInfo storage position = positions[tokenId];

        // Decrease liquidity
        INonfungiblePositionManager.DecreaseLiquidityParams memory decreaseParams =
            INonfungiblePositionManager.DecreaseLiquidityParams({
                tokenId: tokenId,
                liquidity: liquidity,
                amount0Min: amount0Min,
                amount1Min: amount1Min,
                deadline: deadline
            });

        (amount0, amount1) = positionManager.decreaseLiquidity(decreaseParams);

        // Collect tokens
        INonfungiblePositionManager.CollectParams memory collectParams =
            INonfungiblePositionManager.CollectParams({
                tokenId: tokenId,
                recipient: address(this),
                amount0Max: type(uint128).max,
                amount1Max: type(uint128).max
            });

        (uint256 collected0, uint256 collected1) = positionManager.collect(collectParams);

        // Update position liquidity
        position.liquidity -= liquidity;

        // Calculate protocol fee
        uint256 fee0 = (collected0 * protocolFeeBps) / BASIS_POINTS;
        uint256 fee1 = (collected1 * protocolFeeBps) / BASIS_POINTS;

        // Transfer fees to fee distributor
        if (fee0 > 0) {
            IERC20(position.token0).safeTransfer(feeDistributor, fee0);
        }
        if (fee1 > 0) {
            IERC20(position.token1).safeTransfer(feeDistributor, fee1);
        }

        // Transfer remaining tokens to user
        uint256 userAmount0 = collected0 - fee0;
        uint256 userAmount1 = collected1 - fee1;

        if (userAmount0 > 0) {
            if (position.token0 == address(WETH)) {
                WETH.withdraw(userAmount0);
                payable(msg.sender).transfer(userAmount0);
            } else {
                IERC20(position.token0).safeTransfer(msg.sender, userAmount0);
            }
        }

        if (userAmount1 > 0) {
            if (position.token1 == address(WETH)) {
                WETH.withdraw(userAmount1);
                payable(msg.sender).transfer(userAmount1);
            } else {
                IERC20(position.token1).safeTransfer(msg.sender, userAmount1);
            }
        }

        emit LiquidityRemoved(msg.sender, tokenId, liquidity, userAmount0, userAmount1);

        return (userAmount0, userAmount1);
    }

    /**
     * @notice Mengumpulkan fee dari posisi
     * @param tokenId ID posisi NFT
     * @return amount0 Jumlah token0 fee yang dikumpulkan
     * @return amount1 Jumlah token1 fee yang dikumpulkan
     */
    function collectFees(uint256 tokenId) external nonReentrant returns (uint256 amount0, uint256 amount1) {
        require(positions[tokenId].owner == msg.sender, "LiquidityManager: bukan pemilik posisi");

        PositionInfo memory position = positions[tokenId];

        INonfungiblePositionManager.CollectParams memory params =
            INonfungiblePositionManager.CollectParams({
                tokenId: tokenId,
                recipient: address(this),
                amount0Max: type(uint128).max,
                amount1Max: type(uint128).max
            });

        (amount0, amount1) = positionManager.collect(params);

        // Calculate protocol fee
        uint256 protocolFee0 = (amount0 * protocolFeeBps) / BASIS_POINTS;
        uint256 protocolFee1 = (amount1 * protocolFeeBps) / BASIS_POINTS;

        // Transfer protocol fees
        if (protocolFee0 > 0) {
            IERC20(position.token0).safeTransfer(feeDistributor, protocolFee0);
        }
        if (protocolFee1 > 0) {
            IERC20(position.token1).safeTransfer(feeDistributor, protocolFee1);
        }

        // Transfer remaining fees to user
        uint256 userFee0 = amount0 - protocolFee0;
        uint256 userFee1 = amount1 - protocolFee1;

        if (userFee0 > 0) {
            if (position.token0 == address(WETH)) {
                WETH.withdraw(userFee0);
                payable(msg.sender).transfer(userFee0);
            } else {
                IERC20(position.token0).safeTransfer(msg.sender, userFee0);
            }
        }

        if (userFee1 > 0) {
            if (position.token1 == address(WETH)) {
                WETH.withdraw(userFee1);
                payable(msg.sender).transfer(userFee1);
            } else {
                IERC20(position.token1).safeTransfer(msg.sender, userFee1);
            }
        }

        emit FeesCollected(msg.sender, tokenId, userFee0, userFee1);
    }

    /**
     * @notice Mendapatkan posisi user
     * @param user Alamat user
     * @return Array tokenId posisi user
     */
    function getUserPositions(address user) external view returns (uint256[] memory) {
        return userPositions[user];
    }

    /**
     * @notice Update fee distributor (hanya owner)
     * @param _feeDistributor Alamat fee distributor baru
     */
    function updateFeeDistributor(address _feeDistributor) external onlyOwner {
        require(_feeDistributor != address(0), "LiquidityManager: fee distributor tidak boleh zero");
        feeDistributor = _feeDistributor;
    }

    /**
     * @notice Update protocol fee (hanya owner)
     * @param _protocolFeeBps Protocol fee baru dalam basis points
     */
    function updateProtocolFee(uint256 _protocolFeeBps) external onlyOwner {
        require(_protocolFeeBps <= 1000, "LiquidityManager: fee terlalu tinggi"); // Max 10%
        protocolFeeBps = _protocolFeeBps;
    }
}
