# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Build outputs
dist/
build/
out/
.next/
.nuxt/
.vuepress/dist/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.env.*.local

# IDE and Editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs/
*.log

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output/

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# Hardhat files
cache/
artifacts/
typechain/
typechain-types/

# Foundry files
out/
cache_forge/

# Solidity coverage
coverage.json

# Temporary folders
tmp/
temp/

# Frontend specific
frontend/dist/
frontend/build/
frontend/.vite/
frontend/node_modules/

# Smart contract deployment files
deployments/
.openzeppelin/

# Private keys and sensitive data
*.key
*.pem
mnemonic.txt
private-keys.txt

# Local development
.local/
.cache/

# Testing
test-results/
playwright-report/
test-results.xml
