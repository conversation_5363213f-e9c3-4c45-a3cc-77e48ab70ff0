# 🦄 UbaSwap DEX - Decentralized Exchange

A modern, full-featured decentralized exchange (DEX) built on Ethereum Sepolia testnet with real-time data integration and comprehensive DeFi features.

## 🌟 Features

### 💱 **Swap & Trading**
- Real-time token swapping with live price feeds
- Support for 12+ popular Sepolia testnet tokens
- Custom token import functionality
- Slippage protection and deadline settings
- Real-time balance updates every 3 seconds

### 💧 **Liquidity Management**
- Add/Remove liquidity for ETH/UBA pairs
- Real-time pool statistics and reserves
- User pool share calculations
- Fee collection mechanisms
- Percentage-based liquidity removal

### ⚡ **Swap & Forward**
- Swap tokens and send directly to another address
- Batch transaction optimization
- Custom recipient address support
- Gas-efficient forwarding mechanism

### 💰 **Fee Management**
- Protocol fee distribution system
- Admin fee management (owner-only)
- UBA token burning mechanism
- Real-time fee statistics

## 🏗️ **Architecture**

### **Smart Contracts**
- **UBA Token**: ERC20 token contract
- **SwapAndForward**: Core trading engine
- **LiquidityManager**: Pool management system
- **FeeDistributor**: Fee collection and distribution

### **Frontend Stack**
- **React 18** with modern hooks
- **Wagmi v1** for blockchain integration
- **RainbowKit** for wallet connections
- **Vite** for fast development
- **Ethers.js** for contract interactions

### **Data Sources**
- **Alchemy RPC** for real-time blockchain data
- **Etherscan API** for balance verification
- **CoinGecko API** for price feeds
- **WebSocket** connections for live updates

## 🚀 **Quick Start**

### **Prerequisites**
- Node.js 18+ 
- MetaMask or compatible wallet
- Sepolia testnet ETH

### **Installation**

```bash
# Clone the repository
git clone <repository-url>
cd dex-smartcontract

# Install frontend dependencies
cd frontend
npm install

# Start development server
npm run dev
```

### **Smart Contract Deployment**

```bash
# Install Hardhat dependencies
npm install

# Compile contracts
npx hardhat compile

# Deploy to Sepolia
npx hardhat run scripts/deploy.js --network sepolia
```

## 📱 **Usage**

### **1. Connect Wallet**
- Open http://localhost:3003
- Click "Connect Wallet"
- Select MetaMask or preferred wallet
- Switch to Sepolia testnet

### **2. Get Test Tokens**
- Get Sepolia ETH from faucet
- Swap ETH for UBA tokens
- Add liquidity to earn fees

### **3. Trading**
- Select tokens to swap
- Enter amount or click "MAX"
- Adjust slippage if needed
- Confirm transaction in wallet

### **4. Liquidity Provision**
- Go to "Liquidity" tab
- Add ETH/UBA liquidity
- Earn fees from trades
- Remove liquidity anytime

## 🔧 **Configuration**

### **Contract Addresses (Sepolia)**
```javascript
UBA_TOKEN: '******************************************'
SWAP_AND_FORWARD: '******************************************'
LIQUIDITY_MANAGER: '******************************************'
FEE_DISTRIBUTOR: '******************************************'
```

### **Supported Tokens**
- ETH (Native)
- UBA (Custom DEX token)
- USDC, USDT, DAI (Stablecoins)
- WETH, LINK, UNI, AAVE, COMP, MATIC, SHIB

## 🛠️ **Development**

### **Project Structure**
```
dex-smartcontract/
├── contracts/              # Smart contracts
│   ├── UBAToken.sol
│   ├── SwapAndForward.sol
│   ├── LiquidityManager.sol
│   └── FeeDistributor.sol
├── frontend/               # React frontend
│   ├── src/
│   │   ├── components/     # React components
│   │   ├── hooks/          # Custom hooks
│   │   ├── config/         # Configuration files
│   │   └── styles/         # CSS styles
│   └── public/
├── scripts/                # Deployment scripts
└── test/                   # Contract tests
```

### **Key Features**
- **Real-time Data**: 3-second balance updates
- **Smart Contract Integration**: Full ABI support
- **Wallet Integration**: MetaMask + RainbowKit
- **Mobile Responsive**: Works on all devices
- **Error Handling**: Comprehensive error management
- **Debug Mode**: Console logging for development

## 🔐 **Security**

- Row Level Security (RLS) implementation
- JWT token validation
- Slippage protection mechanisms
- Deadline-based transaction expiry
- Owner-only admin functions

## 🌐 **Networks**

Currently deployed on:
- **Sepolia Testnet** (Primary)
- Mainnet deployment ready

## 📊 **Monitoring**

- Real-time transaction monitoring
- Pool statistics tracking
- Fee collection analytics
- User activity metrics

## 🤝 **Contributing**

1. Fork the repository
2. Create feature branch
3. Commit changes
4. Push to branch
5. Create Pull Request

## 📄 **License**

This project is licensed under the MIT License.

## 🔗 **Links**

- **Frontend**: http://localhost:3003
- **Sepolia Etherscan**: https://sepolia.etherscan.io/
- **Sepolia Faucet**: https://sepoliafaucet.com/

## ⚠️ **Disclaimer**

This is a testnet application for educational and development purposes. Use test funds only.

---

**Built with ❤️ for the DeFi community**
